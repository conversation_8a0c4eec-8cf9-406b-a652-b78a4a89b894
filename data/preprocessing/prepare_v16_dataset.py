import os
import pandas as pd
import yaml
from pathlib import Path
from typing import List, Dict

class DatasetPreparator:
    """Dataset preparation class for thyroid nodule data."""
    
    def __init__(self, config_path: str = None):
        """Initialize the dataset preparator.
        
        Args:
            config_path: Path to the configuration file. If None, uses default path.
        """
        if config_path is None:
            config_path = Path(__file__).parent.parent.parent / 'config' / 'data_paths.yaml'
        
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)['data']
            
        self.required_cols = [
            'bom', 'ti_rads', 'std_composition', 'std_echo', 
            'std_ratio', 'std_margin', 'std_foci', 'sop_uid'
        ]
        
        # Create output directories if they don't exist
        os.makedirs(os.path.dirname(self.config['processed']['merged_output_excel']), exist_ok=True)
        os.makedirs(os.path.dirname(self.config['processed']['final_annotations_csv']), exist_ok=True)

    def load_datasets(self) -> tuple:
        """Load main and additional datasets.
        
        Returns:
            tuple: (main_dataset, additional_dataset)
        """
        print("Loading datasets...")
        try:
            df_main = pd.read_excel(self.config['main_dataset_path'])
            df_additional = pd.read_excel(self.config['additional_dataset_path'])
            print(f"Main dataset shape: {df_main.shape}")
            print(f"Additional dataset shape: {df_additional.shape}")
            return df_main, df_additional
        except FileNotFoundError as e:
            print(f"Error: Could not find file {e.filename}. Aborting.")
            raise

    def validate_dataset(self, df: pd.DataFrame) -> bool:
        """Validate that the dataset contains all required columns.
        
        Args:
            df: DataFrame to validate
            
        Returns:
            bool: True if validation passes, False otherwise
        """
        print("\nValidating columns in dataset...")
        missing_cols = [col for col in self.required_cols if col not in df.columns]
        if missing_cols:
            print(f"Error: The dataset is missing required columns: {missing_cols}. Aborting.")
            return False
        print("Validation successful. All required columns are present.")
        return True

    def merge_datasets(self, df_main: pd.DataFrame, df_additional: pd.DataFrame) -> pd.DataFrame:
        """Merge main and additional datasets.
        
        Args:
            df_main: Main dataset
            df_additional: Additional dataset
            
        Returns:
            pd.DataFrame: Merged dataset
        """
        print("\nMerging datasets...")
        df_merged = pd.concat([df_main, df_additional], ignore_index=True)
        print(f"Merged dataset shape: {df_merged.shape}")
        return df_merged

    def generate_image_paths(self, df: pd.DataFrame) -> pd.DataFrame:
        """Generate nodule image paths for the dataset.
        
        Args:
            df: Input DataFrame
            
        Returns:
            pd.DataFrame: DataFrame with added nodule_image_path column
        """
        print("\nGenerating 'nodule_image_path' column...")
        
        def create_nodule_path(row):
            return os.path.join(
                self.config['nodule_image_base_dir'], 
                str(row['bom']), 
                f"{row['sop_uid']}.jpg"
            )
            
        df['nodule_image_path'] = df.apply(create_nodule_path, axis=1)
        print("Path generation complete. Example path:")
        print(df['nodule_image_path'].iloc[0])
        return df

    def save_datasets(self, df: pd.DataFrame) -> None:
        """Save the processed datasets.
        
        Args:
            df: DataFrame to save
        """
        print(f"\nSaving merged data to '{self.config['processed']['merged_output_excel']}'...")
        df.to_excel(self.config['processed']['merged_output_excel'], index=False)
        print("Save complete.")

        print(f"\nSaving final annotations to '{self.config['processed']['final_annotations_csv']}'...")
        df.to_csv(self.config['processed']['final_annotations_csv'], index=False)
        print("Save complete.")

    def process(self) -> None:
        """Main processing function."""
        try:
            # Load datasets
            df_main, df_additional = self.load_datasets()
            
            # Validate additional dataset
            if not self.validate_dataset(df_additional):
                return
                
            # Merge datasets
            df_merged = self.merge_datasets(df_main, df_additional)
            
            # Generate image paths
            df_merged = self.generate_image_paths(df_merged)
            
            # Save processed datasets
            self.save_datasets(df_merged)
            
            print("\nData preparation finished successfully!")
            
        except Exception as e:
            print(f"\nError during data preparation: {str(e)}")
            raise

def main():
    """Main entry point."""
    preparator = DatasetPreparator()
    preparator.process()

if __name__ == '__main__':
    main() 