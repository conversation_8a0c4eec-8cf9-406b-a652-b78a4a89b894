import pandas as pd
import numpy as np
import re
import os
from pathlib import Path
from typing import Tuple, Optional
from tqdm import tqdm

class PositionMapper:
    """Class for mapping descriptive positions to coordinates."""
    
    def __init__(self):
        """Initialize the position mapper with coordinate mapping rules."""
        self.ud_map = {
            '上极': 1, '上部': 2, '上段': 2,
            '中上部': 3, '上部及中部': 3, '中上': 3,
            '中部': 4,
            '中下部': 5, '中下': 5,
            '下部': 6, '下段': 6,
            '下极': 7
        }

    def parse_position_to_coordinates(self, position_str: str) -> Tuple[float, float, float]:
        """Parse a descriptive position string into RL, UD, and FB coordinates.
        
        Args:
            position_str: The descriptive text of the nodule's position.
            
        Returns:
            tuple: A tuple containing (rl, ud, fb) coordinates. NaN if a coordinate is not specified.
        """
        if not isinstance(position_str, str) or not position_str:
            return (np.nan, np.nan, np.nan)

        rl, ud, fb = np.nan, np.nan, np.nan

        # --- UD (Up-Down) Mapping ---
        ud_found = False
        for key, val in self.ud_map.items():
            if key in position_str:
                ud = (2 * val - 1) / 14.0
                ud_found = True
                break
        if not ud_found and '横切' in position_str:
            ud = np.nan
            
        # --- FB (Front-Back) Mapping ---
        if any(k in position_str for k in ['前侧', '前方被膜', '前被膜', '前包膜', '近表面']):
            fb = 0.0
        elif any(k in position_str for k in ['近前侧', '近前被膜']):
            fb = 0.25
        elif any(k in position_str for k in ['实质中央区', '未明确前后']):
            fb = 0.5
        elif any(k in position_str for k in ['近背侧', '近背膜处']):
            fb = 0.75
        elif any(k in position_str for k in ['背侧', '后侧', '后被膜']):
            fb = 1.0
        
        # --- RL (Right-Left) Mapping ---
        is_left = '左叶' in position_str
        is_right = '右叶' in position_str
        
        # 1. Near Isthmus
        if '峡部右侧' in position_str or (is_right and '近峡部' in position_str):
            rl = 3.5 / 9
        elif '峡部左侧' in position_str or (is_left and '近峡部' in position_str):
            rl = 5.5 / 9

        # 2. Outer Lobe
        elif ('右叶外侧' in position_str) or (is_right and '外侧' in position_str) or (is_right and '近颈总动脉' in position_str):
            rl = 0.5 / 9
        elif ('左叶外侧' in position_str) or (is_left and '外侧' in position_str) or (is_left and '近颈总动脉' in position_str):
            rl = 8.5 / 9
            
        # 3. Inner Lobe
        elif ('右叶内侧' in position_str) or (is_right and '内侧' in position_str) or (is_right and '近气管' in position_str):
            rl = 2.5 / 9
        elif ('左叶内侧' in position_str) or (is_left and '内侧' in position_str) or (is_left and '近气管' in position_str) or (is_left and '偏内侧' in position_str):
            rl = 6.5 / 9
            
        # 4. Isthmus
        elif '峡部' in position_str:
            rl = 0.5
            
        # 5. General Lobe
        elif is_right:
            rl = 1/6
        elif is_left:
            rl = 5/6

        if not pd.isna(rl) and '纵切' in position_str:
             pass
        elif '纵切' in position_str:
            rl = np.nan

        return (rl, ud, fb)

    def process_file(self, input_path: str, output_path: Optional[str] = None) -> pd.DataFrame:
        """Process an Excel file and map positions to coordinates.
        
        Args:
            input_path: Path to input Excel file
            output_path: Path to save output Excel file. If None, only returns DataFrame.
            
        Returns:
            pd.DataFrame: Processed DataFrame with coordinate columns
        """
        print(f"Reading file: {input_path}")
        df = pd.read_excel(input_path)

        if 'position' not in df.columns:
            raise ValueError("Input file must contain a 'position' column.")

        print("Parsing 'position' column to generate coordinates...")
        tqdm.pandas(desc="Applying coordinate mapping")
        
        # Apply the parsing function
        coords = df['position'].progress_apply(self.parse_position_to_coordinates)
        
        # Assign results to new columns
        df[['rl_label', 'ud_label', 'fb_label']] = pd.DataFrame(coords.tolist(), index=df.index)

        if output_path:
            print(f"Saving processed file to: {output_path}")
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            df.to_excel(output_path, index=False)
            print("Save complete.")

        return df

def main():
    """Main entry point for command line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Convert descriptive positions to coordinates.')
    parser.add_argument('input_file', help='Path to input Excel file')
    parser.add_argument('--output_file', help='Path to output Excel file (optional)')
    
    args = parser.parse_args()
    
    # Generate output file path if not provided
    if not args.output_file:
        base, ext = os.path.splitext(args.input_file)
        args.output_file = f"{base}_coords{ext}"
    
    try:
        mapper = PositionMapper()
        mapper.process_file(args.input_file, args.output_file)
        print(f"Processing complete. File saved to: {args.output_file}")
    except Exception as e:
        print(f"Error: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    main() 