"""
甲状腺结节数据分布分析和可视化脚本
================================

主要功能:
- 分析甲状腺结节数据集的特征分布
- 生成良恶性比例分析图表
- 支持中英文双语标签显示
- 自动排序和分类统计
- 输出高质量可视化图表

核心数据结构:
- FEATURE_META: 特征元数据字典，包含排序和翻译信息
- analyze_and_visualize: 单特征分布分析函数
- create_combined_bom_chart: 良恶性比例组合图表函数
- show_encoding_examples: 编码示例展示函数

分析特征:
- ti_rads: TI-RADS分级 (1-5)
- std_composition: 结节成分 (囊性、实性等)
- std_echo: 回声特征 (低回声、等回声等)
- std_margin: 边缘特征 (光滑、不规则等)
- std_foci: 钙化点特征 (点状强回声、粗大钙化等)
- std_ratio: 纵横比 (宽>高 vs 高>宽)

输入数据:
- nodules_50k_bom_mix_dataset_v16.xlsx: 结节数据集
- 包含特征列和病理良恶性标签

输出:
- matplot_tirads/目录下的分布图表
- *_bom_combined.png: 各特征的良恶性比例图
- 控制台统计信息输出

图表特性:
- 堆叠条形图显示良恶性比例
- 标注总数和恶性率百分比
- 按预定义顺序排列类别
- 双语标签和标题

作者: 项目团队
日期: 2024
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import LabelEncoder, OneHotEncoder

# --- Font Configuration for Chinese Characters ---
# Attempt to set a font that supports Chinese characters to resolve garbled text issues.
try:
    plt.rcParams['font.sans-serif'] = ['PingFang SC', 'SimHei', 'Heiti TC']
    plt.rcParams['axes.unicode_minus'] = False  # To display negative signs correctly
    print("Chinese font set successfully.")
except Exception as e:
    print(f"Could not set Chinese font, will use default. Warning: {e}")


# --- Configuration ---
CONFIG = {
    'input_excel': 'nodules_50k_bom_mix_dataset_v16.xlsx',
    'output_dir': 'matplot_tirads',
}

# --- English Translations & Custom Sorting Order ---
# The order of keys defines the sorting for the plots
FEATURE_META = {
    'ti_rads': {
        'order': ['1', '2', '3', '4', '5'],
        'translation': {'1': 'TI-RADS 1', '2': 'TI-RADS 2', '3': 'TI-RADS 3', '4': 'TI-RADS 4', '5': 'TI-RADS 5'}
    },
    'std_composition': {
        'order': ['囊性', '海绵样', '囊实性', '实性', 'missing'],
        'translation': {'囊性': 'Cystic', '海绵样': 'Spongiform', '囊实性': 'Cystic-Solid', '实性': 'Solid', 'missing': 'Missing'}
    },
    'std_echo': {
        'order': ['无回声', '高回声', '等回声', '低回声', '极低回声', '不均匀', '强回声', 'missing'],
        'translation': {'无回声': 'Anechoic', '高回声': 'Hyperechoic', '等回声': 'Isoechoic', '低回声': 'Hypoechoic', '极低回声': 'Very-Hypoechoic', '不均匀': 'Uneven', '强回声': 'Strongly-Hyperechoic', 'missing': 'Missing'}
    },
    'std_margin': {
        'order': ['光滑', '分叶', '不清', '不规则', '外侵', 'missing'],
        'translation': {'光滑': 'Smooth', '分叶': 'Lobulated', '不清': 'Indistinct', '不规则': 'Irregular', '外侵': 'Extracapsular-Extension', 'missing': 'Missing'}
    },
    'std_foci': {
        'order': ['大彗星尾', '粗大钙化', '边缘钙化', '点状强回声', '大彗星尾,点状强回声', '粗大钙化,点状强回声', '边缘钙化,点状强回声', '粗大钙化,边缘钙化', 'missing'],
        'translation': {'大彗星尾': 'Comet-Tail Artifact', '粗大钙化': 'Macrocalcification', '边缘钙化': 'Rim Calcification', '点状强回声': 'Punctate Echogenic Foci', '大彗星尾,点状强回声': 'Comet-Tail & Punctate', '粗大钙化,点状强回声': 'Macro & Punctate', '边缘钙化,点状强回声': 'Rim & Punctate', '粗大钙化,边缘钙化': 'Macro & Rim', 'missing': 'Missing'}
    },
    'std_ratio': {
        'order': ['0', '1'],
        'translation': {'0': '宽度>高度 (Width > Height)', '1': '高度>宽度 (Height > Width)'}
    }
}


def analyze_and_visualize(df, column_name, output_suffix=''):
    """Analyzes distribution and plots it with English labels."""
    print(f"--- Distribution for {column_name} ---")
    if column_name in df.columns:
        series = df[column_name].copy()
        if series.dtype == 'object':
            series.fillna('missing', inplace=True)
        else:
            series = series.astype(str).fillna('missing')
            
        print("Original labels and counts:")
        print(df[column_name].value_counts(dropna=False))
        
        plot_series = series.copy()
        if column_name in FEATURE_META:
            # Create a mapping for all unique values, defaulting to the value itself if not in FEATURE_META
            unique_vals = series.unique()
            map_dict = {val: FEATURE_META[column_name]['translation'].get(val, val) for val in unique_vals}
            plot_series = plot_series.map(map_dict)

        plt.figure(figsize=(12, 8))
        sns.countplot(y=plot_series, order=plot_series.value_counts().index, palette="viridis")
        plt.title(f'Distribution of {column_name}', fontsize=16)
        plt.xlabel('Count', fontsize=12)
        plt.ylabel(column_name, fontsize=12)
        plt.tight_layout()
        output_path = os.path.join(CONFIG['output_dir'], f'{column_name}_distribution{output_suffix}.png')
        plt.savefig(output_path)
        plt.close()
        print(f"Distribution plot saved to {output_path}\\n")
    else:
        print(f"Column '{column_name}' not found.\\n")


def show_encoding_examples(df, columns):
    """Shows Label and One-Hot encoding for specified columns."""
    print("--- Encoding Examples ---")
    for col in columns:
        if col in df:
            print(f"\\n--- {col} ---")
            series = df[col].copy()
            if series.dtype == 'object':
                series.fillna('missing', inplace=True)
            else:
                series = series.astype(str).fillna('missing')

            # Label Encoding
            le = LabelEncoder()
            encoded_labels = le.fit_transform(series)
            print("Label Encoding (Category -> Index):")
            for i, class_name in enumerate(le.classes_):
                print(f"  '{class_name}' -> {i}")

            # One-Hot Encoding
            ohe = OneHotEncoder(sparse_output=False, handle_unknown='ignore')
            one_hot_encoded = ohe.fit_transform(series.to_numpy().reshape(-1, 1))
            print("\\nOne-Hot Encoding Categories (columns):")
            print(f"  {ohe.get_feature_names_out([col])}")
            print("\\nExample of One-Hot Encoding (first 5 rows):")
            print(one_hot_encoded[:5])
    print("\\n")


def create_combined_bom_chart(df, feature_column, meta, output_dir):
    """
    Creates a single stacked bar chart showing counts and BOM proportions,
    sorted according to a predefined order.
    """
    print(f"--- Generating combined chart for: {feature_column} ---")
    
    # 1. Prepare data
    temp_df = df[[feature_column, 'bom']].copy()
    temp_df.dropna(subset=['bom'], inplace=True)
    temp_df['bom'] = temp_df['bom'].astype(int)
    
    # Fill NA and ensure column is string type for crosstab
    series = temp_df[feature_column]
    if series.dtype == 'object':
        series.fillna('missing', inplace=True)
    else:
        series = series.astype(str).replace('nan', 'missing').fillna('missing')
    temp_df[feature_column] = series

    # 2. Create contingency table
    contingency_table = pd.crosstab(temp_df[feature_column], temp_df['bom'])
    
    # Add any missing categories from the defined order
    for cat in meta['order']:
        if cat not in contingency_table.index:
            contingency_table.loc[cat] = [0, 0]

    # 3. Sort according to predefined order
    contingency_table = contingency_table.reindex(meta['order']).dropna()
    
    # 4. Plotting
    fig, ax = plt.subplots(figsize=(16, 9))
    contingency_table.plot(kind='bar', stacked=True, ax=ax, colormap='coolwarm', width=0.8)
    
    # 5. Add annotations
    for i, bar in enumerate(ax.patches):
        # Annotations are tricky with stacked bars. We iterate through bars.
        # This approach works for two stacked categories (0 and 1)
        if i < len(contingency_table): # Annotate the 'Benign (0)' part
            continue
        
        # This is the 'Malignant (1)' part, which is on top
        idx = i - len(contingency_table)
        category_counts = contingency_table.iloc[idx]
        
        benign_count = category_counts.get(0, 0)
        malignant_count = category_counts.get(1, 0)
        total = benign_count + malignant_count
        
        if total == 0: continue

        malignant_rate = malignant_count / total
        
        # Position for total count annotation
        x_pos = bar.get_x() + bar.get_width() / 2
        y_pos_total = total * 1.02 # Slightly above the bar
        
        # Add total count text
        ax.text(x_pos, y_pos_total, f'N={total}', ha='center', va='bottom', fontsize=10, weight='bold')
        
        # Add malignant rate text inside the malignant bar part
        if malignant_count > 0:
            y_pos_rate = benign_count + malignant_count / 2 # Center of the malignant bar
            ax.text(x_pos, y_pos_rate, f'{malignant_rate:.1%}', ha='center', va='center', fontsize=9, color='white', weight='bold')

    # 6. Finalize plot
    bilingual_labels = []
    for label in contingency_table.index:
        # The translation now contains the bilingual label directly
        translated_label = meta['translation'].get(label, label)
        bilingual_labels.append(translated_label)

    ax.set_xticklabels(bilingual_labels, rotation=45, ha='right')
    english_feature_name = feature_column.replace('_', ' ').title()
    ax.set_title(f'良恶性比例分析 ({english_feature_name} Distribution and Malignancy Rate)', fontsize=18)
    ax.set_xlabel('')
    ax.set_ylabel('结节数量 (Number of Nodules)', fontsize=14)
    ax.legend(title='病理结果 (Pathology)', labels=['良性 (Benign)', '恶性 (Malignant)'])
    plt.tight_layout()
    
    # Save the plot
    output_path = os.path.join(output_dir, f'{feature_column}_bom_combined.png')
    plt.savefig(output_path)
    plt.close()
    print(f"Chart saved to {output_path}\\n")


def main():
    """Main function to load data and generate plots."""
    os.makedirs(CONFIG['output_dir'], exist_ok=True)
    
    try:
        df = pd.read_excel(CONFIG['input_excel'])
        print(f"Successfully loaded {CONFIG['input_excel']}. Shape: {df.shape}")
    except FileNotFoundError:
        print(f"Error: The file '{CONFIG['input_excel']}' was not found.")
        return

    if '病理良恶性' in df.columns and 'bom' not in df.columns:
        df['bom'] = df['病理良恶性']

    # Update translations to be bilingual for direct use
    for feature, meta_dict in FEATURE_META.items():
        new_translation = {}
        for key, value in meta_dict['translation'].items():
            if key == 'missing':
                new_translation[key] = '未提供 (Missing)'
            # For std_ratio, the translation is already bilingual
            elif feature == 'std_ratio':
                 new_translation[key] = value
            else:
                new_translation[key] = f'{key} ({value})'
        meta_dict['translation'] = new_translation

    for feature, meta in FEATURE_META.items():
        create_combined_bom_chart(df, feature, meta, CONFIG['output_dir'])

    print("Analysis complete. All charts have been generated.")


if __name__ == '__main__':
    main() 