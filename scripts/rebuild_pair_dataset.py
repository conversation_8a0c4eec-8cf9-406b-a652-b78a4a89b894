"""
重建配对数据集脚本
===================

功能:
1. 从基础版本 nodules_50k_bom_mix_dataset_v16_pair.xlsx 开始
2. 识别缺失配对的图像（只有一张图像的 access_no + nindex 组合）
3. 在全集 task_sop_0427_115659.xlsx 中查找缺失的配对图像
4. 如果找到配对，则补齐；如果没找到，则删除单张图像
5. 确保最终数据集中每个 access_no + nindex 组合都有两张图像，且 orientation_label 不同
6. 保存为 nodules_50k_bom_mix_dataset_v16_pair_v2.xlsx

作者: AI Assistant
日期: 2024
"""

import pandas as pd
import numpy as np
import os
from typing import Tuple, List, Dict

def load_data() -> Tuple[pd.DataFrame, pd.DataFrame]:
    """加载基础版本和全集数据"""
    print("正在加载数据文件...")
    
    # 加载基础版本
    base_file = 'data/nodules_50k_bom_mix_dataset_v16_pair.xlsx'
    df_base = pd.read_excel(base_file)
    print(f"基础版本加载完成: {len(df_base)} 行")
    
    # 加载全集
    full_file = 'data/task_sop_0427_115659.xlsx'
    df_full = pd.read_excel(full_file)
    print(f"全集加载完成: {len(df_full)} 行")
    
    return df_base, df_full

def analyze_pairs(df: pd.DataFrame) -> Dict:
    """分析配对情况"""
    print("\n分析当前配对情况...")
    
    # 按 access_no 和 nindex 分组
    pair_groups = df.groupby(['access_no', 'nindex']).size()
    
    analysis = {
        'total_groups': len(pair_groups),
        'single_images': (pair_groups == 1).sum(),
        'paired_images': (pair_groups == 2).sum(),
        'multiple_images': (pair_groups >= 3).sum(),
        'single_group_keys': pair_groups[pair_groups == 1].index.tolist(),
        'multiple_group_keys': pair_groups[pair_groups >= 3].index.tolist()
    }
    
    print(f"总组数: {analysis['total_groups']}")
    print(f"单张图像组数: {analysis['single_images']}")
    print(f"配对图像组数: {analysis['paired_images']}")
    print(f"三张及以上组数: {analysis['multiple_images']}")
    
    return analysis

def find_missing_pairs(df_base: pd.DataFrame, df_full: pd.DataFrame, single_groups: List[Tuple]) -> pd.DataFrame:
    """在全集中查找缺失的配对图像"""
    print(f"\n正在查找 {len(single_groups)} 个单张图像的配对...")
    
    found_pairs = []
    
    for access_no, nindex in single_groups:
        # 获取当前单张图像的信息
        current_image = df_base[(df_base['access_no'] == access_no) & (df_base['nindex'] == nindex)].iloc[0]
        current_orientation = current_image.get('orientation_label', None)
        
        # 在全集中查找相同 access_no 和 nindex 的其他图像
        candidates = df_full[(df_full['access_no'] == access_no) & (df_full['nindex'] == nindex)]
        
        # 排除已经在基础版本中的图像
        candidates = candidates[~candidates['sop_uid'].isin(df_base['sop_uid'])]
        
        if len(candidates) > 0:
            print(f"  找到 {access_no}-{nindex} 的 {len(candidates)} 个候选配对图像")
            
            # 如果有多个候选，优先选择不同 orientation 的
            best_candidate = None
            
            # 首先尝试找到不同 orientation 的图像（如果基础版本有 orientation 信息）
            if current_orientation is not None and 'orientation_label' in candidates.columns:
                different_orientation = candidates[candidates['orientation_label'] != current_orientation]
                if len(different_orientation) > 0:
                    best_candidate = different_orientation.iloc[0]
                    print(f"    选择了不同 orientation 的配对图像")
            
            # 如果没找到不同 orientation 的，就选择第一个
            if best_candidate is None:
                best_candidate = candidates.iloc[0]
                print(f"    选择了第一个候选图像")
            
            found_pairs.append(best_candidate)
    
    if found_pairs:
        found_df = pd.DataFrame(found_pairs)
        print(f"总共找到 {len(found_df)} 个配对图像")
        return found_df
    else:
        print("没有找到任何配对图像")
        return pd.DataFrame()

def validate_pairs(df: pd.DataFrame) -> pd.DataFrame:
    """验证并清理配对数据"""
    print("\n验证配对数据...")
    
    # 按 access_no 和 nindex 分组
    groups = df.groupby(['access_no', 'nindex'])
    valid_pairs = []
    
    for (access_no, nindex), group in groups:
        if len(group) == 2:
            # 检查是否有不同的 orientation_label
            if 'orientation_label' in group.columns:
                orientations = group['orientation_label'].unique()
                if len(orientations) == 2 and set(orientations) == {0, 1}:
                    valid_pairs.extend(group.to_dict('records'))
                    continue
            
            # 如果没有 orientation 信息或者 orientation 相同，仍然保留
            valid_pairs.extend(group.to_dict('records'))
        elif len(group) > 2:
            # 如果有多于2张图像，选择最好的配对
            if 'orientation_label' in group.columns:
                # 尝试找到一对不同 orientation 的图像
                orientations = group['orientation_label'].unique()
                if 0 in orientations and 1 in orientations:
                    # 各选一张
                    img_0 = group[group['orientation_label'] == 0].iloc[0]
                    img_1 = group[group['orientation_label'] == 1].iloc[0]
                    valid_pairs.extend([img_0.to_dict(), img_1.to_dict()])
                else:
                    # 如果都是同一个 orientation，选择前两张
                    valid_pairs.extend(group.head(2).to_dict('records'))
            else:
                # 没有 orientation 信息，选择前两张
                valid_pairs.extend(group.head(2).to_dict('records'))
    
    result_df = pd.DataFrame(valid_pairs)
    print(f"验证完成，保留 {len(result_df)} 行数据")
    
    return result_df

def add_pre_orientation_column(df: pd.DataFrame) -> pd.DataFrame:
    """添加 pre_orientation 列"""
    print("\n添加 pre_orientation 列...")
    
    df = df.copy()
    
    # 如果有 orientation_label，直接复制
    if 'orientation_label' in df.columns:
        df['pre_orientation'] = df['orientation_label']
        print("从 orientation_label 复制到 pre_orientation")
    else:
        # 如果没有，设置为 NaN
        df['pre_orientation'] = np.nan
        print("设置 pre_orientation 为 NaN")
    
    return df

def main():
    """主函数"""
    print("开始重建配对数据集...")
    
    # 1. 加载数据
    df_base, df_full = load_data()
    
    # 2. 分析当前配对情况
    analysis = analyze_pairs(df_base)
    
    # 3. 查找缺失的配对图像
    if analysis['single_images'] > 0:
        found_pairs_df = find_missing_pairs(df_base, df_full, analysis['single_group_keys'])
        
        if len(found_pairs_df) > 0:
            # 合并找到的配对图像
            print(f"\n合并 {len(found_pairs_df)} 个找到的配对图像...")
            df_combined = pd.concat([df_base, found_pairs_df], ignore_index=True)
        else:
            df_combined = df_base.copy()
    else:
        print("\n没有单张图像需要查找配对")
        df_combined = df_base.copy()
    
    # 4. 验证和清理配对数据
    df_final = validate_pairs(df_combined)
    
    # 5. 添加 pre_orientation 列
    df_final = add_pre_orientation_column(df_final)
    
    # 6. 最终分析
    print("\n最终数据集分析:")
    final_analysis = analyze_pairs(df_final)
    
    # 7. 保存结果
    output_file = 'data/nodules_50k_bom_mix_dataset_v16_pair_v2.xlsx'
    print(f"\n保存结果到 {output_file}...")
    df_final.to_excel(output_file, index=False)
    print(f"保存完成！最终数据集包含 {len(df_final)} 行数据")
    
    # 8. 生成报告
    print("\n=== 重建报告 ===")
    print(f"原始基础版本: {len(df_base)} 行")
    print(f"找到的配对图像: {len(found_pairs_df) if 'found_pairs_df' in locals() else 0} 行")
    print(f"最终数据集: {len(df_final)} 行")
    print(f"有效配对组数: {final_analysis['paired_images']}")
    print(f"剩余单张图像: {final_analysis['single_images']}")

if __name__ == "__main__":
    main()
