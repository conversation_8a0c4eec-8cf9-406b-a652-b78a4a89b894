"""
重建配对数据集脚本 V3
====================

功能:
1. 从基础版本 nodules_50k_bom_mix_dataset_v16_position.xlsx 开始
2. 识别缺失配对的图像（只有一张图像的 access_no + nindex 组合）
3. 在全集 task_sop_0427_115659.xlsx 中查找缺失的配对图像
4. 如果找到配对，则补齐；如果没找到，则删除单张图像
5. 确保最终数据集中每个 access_no + nindex 组合都有两张图像，且 pre_orientation 不同
6. 保存为 nodules_50k_bom_mix_dataset_v16_pair_v3.xlsx

作者: AI Assistant
日期: 2024
"""

import pandas as pd
import numpy as np
import os
from typing import Tuple, List, Dict

def load_data() -> Tuple[pd.DataFrame, pd.DataFrame]:
    """加载基础版本和全集数据"""
    print("正在加载数据文件...")
    
    # 加载基础版本
    base_file = 'data/nodules_50k_bom_mix_dataset_v16_position.xlsx'
    df_base = pd.read_excel(base_file)
    print(f"基础版本加载完成: {len(df_base)} 行")
    
    # 加载全集
    full_file = 'data/task_sop_0427_115659.xlsx'
    df_full = pd.read_excel(full_file)
    print(f"全集加载完成: {len(df_full)} 行")
    
    return df_base, df_full

def analyze_pairs(df: pd.DataFrame) -> Dict:
    """分析配对情况"""
    print("\n分析当前配对情况...")
    
    # 按 access_no 和 nindex 分组
    pair_groups = df.groupby(['access_no', 'nindex']).size()
    
    analysis = {
        'total_groups': len(pair_groups),
        'single_images': (pair_groups == 1).sum(),
        'paired_images': (pair_groups == 2).sum(),
        'multiple_images': (pair_groups >= 3).sum(),
        'single_group_keys': pair_groups[pair_groups == 1].index.tolist(),
        'multiple_group_keys': pair_groups[pair_groups >= 3].index.tolist()
    }
    
    print(f"总组数: {analysis['total_groups']}")
    print(f"单张图像组数: {analysis['single_images']}")
    print(f"配对图像组数: {analysis['paired_images']}")
    print(f"三张及以上组数: {analysis['multiple_images']}")
    
    return analysis

def find_missing_pairs(df_base: pd.DataFrame, df_full: pd.DataFrame, single_groups: List[Tuple]) -> pd.DataFrame:
    """在全集中查找缺失的配对图像"""
    print(f"\n正在查找 {len(single_groups)} 个单张图像的配对...")
    
    found_pairs = []
    
    for access_no, nindex in single_groups:
        # 获取当前单张图像的信息
        current_image = df_base[(df_base['access_no'] == access_no) & (df_base['nindex'] == nindex)].iloc[0]
        current_orientation = current_image.get('pre_orientation', None)
        
        # 在全集中查找相同 access_no 和 nindex 的其他图像
        candidates = df_full[(df_full['access_no'] == access_no) & (df_full['nindex'] == nindex)]
        
        # 排除已经在基础版本中的图像
        candidates = candidates[~candidates['sop_uid'].isin(df_base['sop_uid'])]
        
        if len(candidates) > 0:
            print(f"  找到 {access_no}-{nindex} 的 {len(candidates)} 个候选配对图像")
            
            # 选择第一个候选图像（因为全集中没有 orientation 信息）
            best_candidate = candidates.iloc[0].copy()
            
            # 为候选图像添加缺失的字段（从基础版本的结构中获取）
            for col in df_base.columns:
                if col not in best_candidate.index:
                    best_candidate[col] = np.nan
            
            # 如果当前图像有 orientation 信息，为配对图像设置不同的 orientation
            if current_orientation is not None and not pd.isna(current_orientation):
                # 设置相反的 orientation
                best_candidate['pre_orientation'] = 1 - current_orientation
                print(f"    为配对图像设置 orientation: {1 - current_orientation}")
            else:
                # 如果当前图像没有 orientation，随机设置
                best_candidate['pre_orientation'] = np.random.choice([0, 1])
                print(f"    随机设置配对图像 orientation: {best_candidate['pre_orientation']}")
            
            found_pairs.append(best_candidate)
    
    if found_pairs:
        found_df = pd.DataFrame(found_pairs)
        print(f"总共找到 {len(found_df)} 个配对图像")
        return found_df
    else:
        print("没有找到任何配对图像")
        return pd.DataFrame()

def clean_multiple_images(df: pd.DataFrame, multiple_groups: List[Tuple]) -> pd.DataFrame:
    """处理有多于2张图像的组，保留最好的配对"""
    print(f"\n处理 {len(multiple_groups)} 个多张图像组...")
    
    df_cleaned = df.copy()
    rows_to_remove = []
    
    for access_no, nindex in multiple_groups:
        group = df_cleaned[(df_cleaned['access_no'] == access_no) & (df_cleaned['nindex'] == nindex)]
        print(f"  处理组 {access_no}-{nindex}: {len(group)} 张图像")
        
        if 'pre_orientation' in group.columns:
            # 尝试找到一对不同 orientation 的图像
            orientations = group['pre_orientation'].dropna().unique()
            
            if len(orientations) >= 2 and 0 in orientations and 1 in orientations:
                # 各选一张不同 orientation 的图像
                img_0 = group[group['pre_orientation'] == 0].iloc[0]
                img_1 = group[group['pre_orientation'] == 1].iloc[0]
                
                # 标记其他图像为删除
                keep_indices = [img_0.name, img_1.name]
                remove_indices = [idx for idx in group.index if idx not in keep_indices]
                rows_to_remove.extend(remove_indices)
                
                print(f"    保留2张不同orientation的图像，删除{len(remove_indices)}张")
            else:
                # 如果都是同一个 orientation 或没有 orientation 信息，保留前两张
                keep_indices = group.index[:2].tolist()
                remove_indices = group.index[2:].tolist()
                rows_to_remove.extend(remove_indices)
                
                print(f"    保留前2张图像，删除{len(remove_indices)}张")
        else:
            # 没有 orientation 信息，保留前两张
            keep_indices = group.index[:2].tolist()
            remove_indices = group.index[2:].tolist()
            rows_to_remove.extend(remove_indices)
            
            print(f"    保留前2张图像，删除{len(remove_indices)}张")
    
    # 删除标记的行
    if rows_to_remove:
        df_cleaned = df_cleaned.drop(rows_to_remove)
        print(f"总共删除了 {len(rows_to_remove)} 行多余的图像")
    
    return df_cleaned

def remove_single_images(df: pd.DataFrame, single_groups: List[Tuple]) -> pd.DataFrame:
    """删除无法找到配对的单张图像"""
    print(f"\n删除 {len(single_groups)} 个无法配对的单张图像...")
    
    df_cleaned = df.copy()
    rows_to_remove = []
    
    for access_no, nindex in single_groups:
        group_indices = df_cleaned[(df_cleaned['access_no'] == access_no) & (df_cleaned['nindex'] == nindex)].index
        rows_to_remove.extend(group_indices.tolist())
        print(f"  删除 {access_no}-{nindex}")
    
    if rows_to_remove:
        df_cleaned = df_cleaned.drop(rows_to_remove)
        print(f"总共删除了 {len(rows_to_remove)} 行单张图像")
    
    return df_cleaned

def validate_pairs(df: pd.DataFrame) -> pd.DataFrame:
    """验证并清理配对数据"""
    print("\n验证配对数据...")
    
    # 按 access_no 和 nindex 分组
    groups = df.groupby(['access_no', 'nindex'])
    valid_pairs = []
    
    for (access_no, nindex), group in groups:
        if len(group) == 2:
            valid_pairs.extend(group.to_dict('records'))
        elif len(group) == 1:
            print(f"  警告: 发现单张图像 {access_no}-{nindex}")
        elif len(group) > 2:
            print(f"  警告: 发现多张图像 {access_no}-{nindex} ({len(group)}张)")
    
    result_df = pd.DataFrame(valid_pairs)
    print(f"验证完成，保留 {len(result_df)} 行数据")
    
    return result_df

def main():
    """主函数"""
    print("开始重建配对数据集 V3...")
    
    # 1. 加载数据
    df_base, df_full = load_data()
    
    # 2. 分析当前配对情况
    analysis = analyze_pairs(df_base)
    
    # 3. 处理多张图像组
    if analysis['multiple_images'] > 0:
        df_base = clean_multiple_images(df_base, analysis['multiple_group_keys'])
        # 重新分析
        analysis = analyze_pairs(df_base)
    
    # 4. 查找缺失的配对图像
    found_pairs_df = pd.DataFrame()
    if analysis['single_images'] > 0:
        found_pairs_df = find_missing_pairs(df_base, df_full, analysis['single_group_keys'])
        
        if len(found_pairs_df) > 0:
            # 合并找到的配对图像
            print(f"\n合并 {len(found_pairs_df)} 个找到的配对图像...")
            df_combined = pd.concat([df_base, found_pairs_df], ignore_index=True)
        else:
            df_combined = df_base.copy()
        
        # 重新分析，找出仍然是单张的图像
        analysis_after = analyze_pairs(df_combined)
        
        # 删除仍然无法配对的单张图像
        if analysis_after['single_images'] > 0:
            df_combined = remove_single_images(df_combined, analysis_after['single_group_keys'])
    else:
        print("\n没有单张图像需要查找配对")
        df_combined = df_base.copy()
    
    # 5. 验证和清理配对数据
    df_final = validate_pairs(df_combined)
    
    # 6. 最终分析
    print("\n最终数据集分析:")
    final_analysis = analyze_pairs(df_final)
    
    # 7. 保存结果
    output_file = 'data/nodules_50k_bom_mix_dataset_v16_pair_v3.xlsx'
    print(f"\n保存结果到 {output_file}...")
    df_final.to_excel(output_file, index=False)
    print(f"保存完成！最终数据集包含 {len(df_final)} 行数据")
    
    # 8. 生成报告
    print("\n=== 重建报告 ===")
    print(f"原始基础版本: {len(df_base)} 行")
    print(f"找到的配对图像: {len(found_pairs_df)} 行")
    print(f"最终数据集: {len(df_final)} 行")
    print(f"有效配对组数: {final_analysis['paired_images']}")
    print(f"剩余单张图像: {final_analysis['single_images']}")

if __name__ == "__main__":
    main()
