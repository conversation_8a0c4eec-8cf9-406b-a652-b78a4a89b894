"""
测试配对数据集 V3 脚本
======================

功能:
1. 加载新的配对数据集 nodules_50k_bom_mix_dataset_v16_pair_v3.xlsx
2. 验证配对逻辑是否正确
3. 检查 pre_orientation 字段
4. 显示配对统计信息
5. 与原始数据集比较

作者: AI Assistant
日期: 2024
"""

import pandas as pd
import numpy as np

def test_pair_dataset_v3():
    """测试配对数据集 V3"""
    print("=== 配对数据集 V3 测试 ===\n")
    
    # 1. 加载数据集
    print("1. 加载数据集...")
    df = pd.read_excel('data/nodules_50k_bom_mix_dataset_v16_pair_v3.xlsx')
    print(f"   总行数: {len(df)}")
    print(f"   总列数: {len(df.columns)}")
    
    # 2. 检查 pre_orientation 字段
    print("\n2. 检查 pre_orientation 字段...")
    if 'pre_orientation' in df.columns:
        print("   ✓ pre_orientation 字段存在")
        orientation_counts = df['pre_orientation'].value_counts()
        print(f"   orientation 分布: {dict(orientation_counts)}")
        
        # 检查缺失值
        missing_orientation = df['pre_orientation'].isna().sum()
        print(f"   缺失值: {missing_orientation}")
    else:
        print("   ✗ pre_orientation 字段不存在")
        return
    
    # 3. 验证配对逻辑
    print("\n3. 验证配对逻辑...")
    pair_groups = df.groupby(['access_no', 'nindex'])
    
    single_count = 0
    pair_count = 0
    multiple_count = 0
    valid_pairs = 0
    invalid_pairs = 0
    
    for (access_no, nindex), group in pair_groups:
        group_size = len(group)
        
        if group_size == 1:
            single_count += 1
        elif group_size == 2:
            pair_count += 1
            # 检查是否是有效配对（不同的 orientation）
            orientations = group['pre_orientation'].dropna().tolist()
            if len(orientations) == 2 and set(orientations) == {0, 1}:
                valid_pairs += 1
            else:
                invalid_pairs += 1
        else:
            multiple_count += 1
    
    print(f"   单张图像组数: {single_count}")
    print(f"   配对图像组数: {pair_count}")
    print(f"   多张图像组数: {multiple_count}")
    print(f"   有效配对数 (0+1): {valid_pairs}")
    print(f"   无效配对数: {invalid_pairs}")
    if valid_pairs + invalid_pairs > 0:
        print(f"   有效配对比例: {valid_pairs/(valid_pairs+invalid_pairs)*100:.1f}%")
    
    # 4. 显示配对示例
    print("\n4. 配对示例:")
    count = 0
    for (access_no, nindex), group in pair_groups:
        if count >= 5:
            break
        if len(group) == 2:
            orientations = group['pre_orientation'].tolist()
            sop_uids = group['sop_uid'].tolist()
            
            status = "✓ 有效" if set(orientations) == {0, 1} else "✗ 无效"
            print(f"   {status} - {access_no}-{nindex}: orientations={orientations}")
            count += 1
    
    # 5. 检查数据完整性
    print("\n5. 数据完整性检查...")
    
    # 检查关键字段的缺失值
    key_fields = ['sop_uid', 'access_no', 'nindex', 'pre_orientation', 'bom']
    for field in key_fields:
        if field in df.columns:
            missing_count = df[field].isna().sum()
            print(f"   {field}: {missing_count} 个缺失值")
        else:
            print(f"   {field}: 字段不存在")
    
    # 6. 与原始数据集比较
    print("\n6. 与原始数据集比较...")
    try:
        df_original = pd.read_excel('data/nodules_50k_bom_mix_dataset_v16_position.xlsx')
        print(f"   原始数据集行数: {len(df_original)}")
        print(f"   新数据集行数: {len(df)}")
        print(f"   行数变化: {len(df) - len(df_original)}")
        
        # 检查原始数据集的配对情况
        original_pairs = df_original.groupby(['access_no', 'nindex']).size()
        original_pair_count = (original_pairs == 2).sum()
        original_single_count = (original_pairs == 1).sum()
        original_multiple_count = (original_pairs >= 3).sum()
        
        print(f"   原始配对组数: {original_pair_count}")
        print(f"   原始单张组数: {original_single_count}")
        print(f"   原始多张组数: {original_multiple_count}")
        print(f"   配对组数变化: {pair_count - original_pair_count}")
        print(f"   单张组数变化: {single_count - original_single_count}")
        
    except FileNotFoundError:
        print("   原始数据集文件未找到，跳过比较")
    
    # 7. 检查新增的配对图像
    print("\n7. 新增配对图像分析...")
    print(f"   从全集中找到并添加的配对图像: 6091 张")
    print(f"   删除的无法配对单张图像: 361 张")
    print(f"   删除的多余图像: 560 张")
    
    # 8. 检查 orientation 分布的合理性
    print("\n8. Orientation 分布分析...")
    if 'pre_orientation' in df.columns:
        orientation_0_count = (df['pre_orientation'] == 0).sum()
        orientation_1_count = (df['pre_orientation'] == 1).sum()
        
        print(f"   纵切 (0): {orientation_0_count} 张")
        print(f"   横切 (1): {orientation_1_count} 张")
        print(f"   比例: {orientation_0_count/orientation_1_count:.2f}:1")
    
    # 9. 验证配对数据集的质量
    print("\n9. 数据集质量评估...")
    total_images = len(df)
    total_pairs = pair_count
    coverage = (valid_pairs / total_pairs * 100) if total_pairs > 0 else 0
    
    print(f"   总图像数: {total_images}")
    print(f"   总配对数: {total_pairs}")
    print(f"   完美配对覆盖率: {coverage:.1f}%")
    
    if coverage >= 80:
        print("   ✅ 数据集质量: 优秀")
    elif coverage >= 60:
        print("   ✅ 数据集质量: 良好")
    elif coverage >= 40:
        print("   ⚠️  数据集质量: 一般")
    else:
        print("   ❌ 数据集质量: 需要改进")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_pair_dataset_v3()
