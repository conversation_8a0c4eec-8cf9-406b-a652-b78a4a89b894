"""
批量推理脚本 - 双模型结节位置预测流程
===================================

主要功能:
- 结合分类器和回归器进行结节位置预测的完整流程
- 阶段1：使用位置分类器预测切面方向
- 阶段2：使用回归器预测3D坐标(RL,UD,FB)
- 阶段3：组装位置字符串并进行结节配对分析

核心数据结构:
- ThyroidPositionClassifier: 位置分类模型（仅方向预测头）
- ThyroidPositionRegressor: 3D坐标回归模型
- NoduleInferenceDataset: 批量推理数据集
- assemble_position_from_coords: 坐标到位置字符串转换函数

输入数据:
- nodules_50k_bom_mix_dataset_v16.xlsx: 结节数据文件
- access_no, sop_uid: 图像标识符
- 超声图像文件

输出数据:
- pre_orientation, pre_orientation_conf: 切面方向预测和置信度
- pre_RL, pre_UD, pre_FB: 3D坐标预测
- pre_position: 组装的位置字符串
- pair_RL, pair_UD, pair_FB, pair_position: 配对结节的融合结果

配对逻辑:
- 按access_no和nindex分组
- 要求一个纵切+一个横切的配对
- 融合坐标：RL来自横切，UD来自纵切，FB取平均值

作者: 项目团队
日期: 2024
"""

import os
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from torchvision import models, transforms
from PIL import Image
from tqdm import tqdm
import warnings

warnings.filterwarnings("ignore", category=UserWarning, module='torchvision')

# --- Configuration ---
DATASET_FILE = '/Users/<USER>/Documents/PycharmProjects/us_position_classify/nodules_50k_bom_mix_dataset_v16.xlsx'
OUTPUT_FILE = '/Users/<USER>/Documents/PycharmProjects/us_position_classify/nodules_50k_bom_mix_dataset_v16_position.xlsx'
US_IMAGE_BASE = '/Users/<USER>/Documents/images/nodule_190k/us_images'
# --- Two-Model Configuration ---
CLASSIFICATION_MODEL_PATH = '/Users/<USER>/Documents/PycharmProjects/us_position_classify/models/thyroid_position_model_crop_v1.pth'
REGRESSION_MODEL_PATH = '/Users/<USER>/Documents/PycharmProjects/us_position_classify/models_reg/thyroid_position_regressor_v1.pth'

BATCH_SIZE = 64
IMG_SIZE = 224
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "mps" if torch.backends.mps.is_available() else "cpu")
DEBUG_MODE = False # Set to False to run on the full dataset

# --- Model Definition: Multi-head Classifier (from train_position_classifier_v2.py) ---
class ThyroidPositionClassifier(nn.Module):
    def __init__(self, label_maps):
        super(ThyroidPositionClassifier, self).__init__()
        self.base_model = models.resnet50(weights=models.ResNet50_Weights.DEFAULT)
        num_features = self.base_model.fc.in_features
        self.base_model.fc = nn.Identity()
        self.dropout = nn.Dropout(p=0.2)
        
        # We only need the orientation head for this script
        self.orientation_classifier = nn.Linear(num_features, len(label_maps['orientation']))
        
    def forward(self, x):
        features = self.dropout(self.base_model(x))
        # Only return the logits we care about
        orientation_logits = self.orientation_classifier(features)
        return orientation_logits

# --- Model Definition: Regressor (from train_position_regressor.py) ---
class ThyroidPositionRegressor(nn.Module):
    def __init__(self):
        super(ThyroidPositionRegressor, self).__init__()
        self.base_model = models.resnet50(weights=models.ResNet50_Weights.DEFAULT)
        num_features = self.base_model.fc.in_features
        self.base_model.fc = nn.Identity()
        self.regressor = nn.Sequential(
            nn.Dropout(p=0.5),
            nn.Linear(num_features, 3),
            nn.Sigmoid()
        )
    def forward(self, x):
        features = self.base_model(x)
        coordinates = self.regressor(features)
        return coordinates

# --- Position Assembly Logic (adapted from label_viewer.py) ---
def assemble_position_from_coords(rl, ud, fb):
    """Assembles a position string from regressed RLUDFB coordinates."""
    if pd.isna(rl) and pd.isna(ud) and pd.isna(fb):
        return np.nan
    lobe_part, rl_side_part = "", ""
    if pd.isna(rl): lobe_part = "N/A"
    elif rl < 1/9: lobe_part, rl_side_part = "右叶", "外侧"
    elif rl < 2/9: lobe_part = "右叶"
    elif rl < 3/9: lobe_part, rl_side_part = "右叶", "近峡部" if not pd.isna(fb) and fb < 0.4 else "内侧"
    elif rl < 4/9: lobe_part = "峡部偏右"
    elif rl < 5/9: lobe_part = "峡部"
    elif rl < 6/9: lobe_part = "峡部偏左"
    elif rl < 7/9: lobe_part, rl_side_part = "左叶", "近峡部" if not pd.isna(fb) and fb < 0.4 else "内侧"
    elif rl < 8/9: lobe_part = "左叶"
    else: lobe_part, rl_side_part = "左叶", "外侧"
    ud_part = ""
    if not pd.isna(ud):
        if ud < 1/7: ud_part = "上极"
        elif ud < 2/7: ud_part = "上部"
        elif ud < 3/7: ud_part = "中上部"
        elif ud < 4/7: ud_part = "中部"
        elif ud < 5/7: ud_part = "中下部"
        elif ud < 6/7: ud_part = "下部"
        else: ud_part = "下极"
    fb_part = ""
    if not pd.isna(fb):
        if fb < 0.125: fb_part = "前侧"
        elif fb < 0.375: fb_part = "近前侧"
        elif fb < 0.625: fb_part = ""
        elif fb < 0.875: fb_part = "近背侧"
        else: fb_part = "背侧"
    final_side_part = fb_part if fb_part else rl_side_part
    final_parts = [lobe_part, ud_part, final_side_part]
    final_position = "".join(p for p in final_parts if p)
    return final_position if final_position else "N/A"

# --- Custom Dataset for Inference ---
class NoduleInferenceDataset(Dataset):
    def __init__(self, dataframe, img_root, transform=None):
        self.dataframe = dataframe
        self.img_root = img_root
        self.transform = transform
    def __len__(self):
        return len(self.dataframe)
    def __getitem__(self, idx):
        row = self.dataframe.iloc[idx]
        img_path = os.path.join(self.img_root, str(row['access_no']), f"{row['sop_uid']}.jpg")
        try:
            image = Image.open(img_path).convert('RGB')
            if self.transform: image = self.transform(image)
        except (FileNotFoundError, IOError):
            image = torch.zeros((3, IMG_SIZE, IMG_SIZE))
        return image, idx

# --- Inference Function ---
def run_inference(model, data_loader, task='classification'):
    all_preds = []
    with torch.no_grad():
        for images, indices in tqdm(data_loader, desc=f"Inferring ({task})"):
            images = images.to(DEVICE)
            outputs = model(images)
            
            if task == 'classification':
                probs = torch.softmax(outputs, dim=1)
                confs, preds = torch.max(probs, dim=1)
                preds_cpu, confs_cpu = preds.cpu().numpy(), confs.cpu().numpy()
                for i in range(len(indices)):
                    all_preds.append({'df_index': indices[i].item(), 'pre_orientation': preds_cpu[i], 'pre_orientation_conf': confs_cpu[i]})
            
            elif task == 'regression':
                coords_cpu = outputs.cpu().numpy()
                for i in range(len(indices)):
                    all_preds.append({'df_index': indices[i].item(), 'pre_RL': coords_cpu[i, 0], 'pre_UD': coords_cpu[i, 1], 'pre_FB': coords_cpu[i, 2]})
    
    return pd.DataFrame(all_preds).set_index('df_index')

# --- Main Script ---
def main():
    print(f"Using device: {DEVICE}")

    # --- Load Data ---
    print(f"Loading dataset from {DATASET_FILE}...")
    try:
        df = pd.read_excel(DATASET_FILE)
    except FileNotFoundError:
        print(f"ERROR: Dataset file not found at {DATASET_FILE}")
        return

    if DEBUG_MODE:
        print("--- RUNNING IN DEBUG MODE: Processing first 100 records ---")
        df = df.head(100)

    transform = transforms.Compose([
        transforms.Resize((IMG_SIZE, IMG_SIZE)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    dataset = NoduleInferenceDataset(df, US_IMAGE_BASE, transform=transform)
    data_loader = DataLoader(dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=4, pin_memory=True)

    # --- STAGE 1: Orientation Classification ---
    print("\n--- Stage 1: Predicting Orientation ---")
    try:
        # Create a dummy label_map, as the model needs it for initialization
        dummy_label_maps = {'orientation': {'Longitudinal': 0, 'Transverse': 1}}
        classifier_model = ThyroidPositionClassifier(dummy_label_maps).to(DEVICE)
        
        # Load the state dict, but only for the orientation classifier head
        state_dict = torch.load(CLASSIFICATION_MODEL_PATH, map_location=DEVICE)
        # We need to filter the state_dict to only load the weights for the parts of the model we have defined.
        orientation_weights = {k: v for k, v in state_dict.items() if 'orientation_classifier' in k or 'base_model' in k}
        classifier_model.load_state_dict(orientation_weights, strict=False) # strict=False allows partial loading
        classifier_model.eval()
        orientation_preds_df = run_inference(classifier_model, data_loader, task='classification')
        df = df.join(orientation_preds_df)
    except Exception as e:
        print(f"ERROR during orientation prediction: {e}")
        return

    # --- STAGE 2: Coordinate Regression ---
    print("\n--- Stage 2: Predicting Coordinates (RL, UD, FB) ---")
    try:
        regressor_model = ThyroidPositionRegressor().to(DEVICE)
        regressor_model.load_state_dict(torch.load(REGRESSION_MODEL_PATH, map_location=DEVICE))
        regressor_model.eval()
        regression_preds_df = run_inference(regressor_model, data_loader, task='regression')
        df = df.join(regression_preds_df)
    except Exception as e:
        print(f"ERROR during coordinate regression: {e}")
        return

    # --- STAGE 3: Calculate Position Strings and Pair Nodules ---
    print("\n--- Stage 3: Assembling positions and pairing nodules ---")
    
    # Calculate pre_position string from regressed coordinates
    df['pre_position'] = df.apply(
        lambda row: assemble_position_from_coords(row['pre_RL'], row['pre_UD'], row['pre_FB']),
        axis=1
    )

    # Initialize pairing columns
    for col in ['pair_RL', 'pair_UD', 'pair_FB', 'pair_position', 'pair_orientation_conf']:
        df[col] = np.nan
    
    # Explicitly set the dtype for the position string column to avoid future errors
    df['pair_position'] = df['pair_position'].astype('object')

    # Group by nodule identifier and pair
    grouped = df.groupby(['access_no', 'nindex'])
    for _, group in tqdm(grouped, desc="Pairing Nodules"):
        if len(group) == 2:
            orientations = set(group['pre_orientation'].unique())
            if orientations == {0, 1}:
                long_row = group[group['pre_orientation'] == 0].iloc[0]
                trans_row = group[group['pre_orientation'] == 1].iloc[0]
                pair_rl = trans_row['pre_RL']
                pair_ud = long_row['pre_UD']
                pair_fb = np.mean([long_row['pre_FB'], trans_row['pre_FB']])
                pair_pos_str = assemble_position_from_coords(pair_rl, pair_ud, pair_fb)
                pair_conf = np.mean([long_row['pre_orientation_conf'], trans_row['pre_orientation_conf']])
                
                df.loc[group.index, ['pair_RL', 'pair_UD', 'pair_FB', 'pair_position', 'pair_orientation_conf']] = [pair_rl, pair_ud, pair_fb, pair_pos_str, pair_conf]

    # --- Save Results ---
    print(f"\nSaving final results to {OUTPUT_FILE}...")
    try:
        df.to_excel(OUTPUT_FILE, index=False)
        print("Processing complete. File saved successfully.")
    except Exception as e:
        print(f"ERROR: Could not save the file. {e}")

if __name__ == '__main__':
    main() 