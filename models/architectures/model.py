"""
甲状腺多任务分类模型定义
======================

主要功能:
- 定义5头多任务分类神经网络模型
- 基于ResNet50骨干网络
- 用于甲状腺结节位置的多维度分类

模型架构:
- 输入：RGB图像 (3通道)
- 骨干网络：ResNet50 (预训练权重)
- 特征提取：2048维特征向量
- 输出头：5个独立的分类器

分类任务:
- orientation_classifier: 切面方向分类 (2类: 纵切/横切)
- lobe_classifier: 叶位分类 (3类: 左叶/峡部/右叶)
- longitudinal_classifier: 纵向位置分类 (7类: 上极到下极)
- transverse_classifier: 横向位置分类 (5类: 前侧到内侧)
- isthmus_classifier: 峡部关系分类 (2类: 近峡部/非近峡部)

数据流:
输入图像 -> ResNet50特征提取 -> 5个并行分类头 -> 多任务输出

使用场景:
- 训练时：计算多任务损失
- 推理时：同时获得所有位置属性预测
- 可视化时：分析各任务的预测置信度

作者: 项目团队  
日期: 2024
"""

import torch
import torch.nn as nn
import torchvision.models as models

class ThyroidModel(nn.Module):
    def __init__(self):
        super(ThyroidModel, self).__init__()
        # Load a ResNet-50 model, as indicated by the state_dict keys.
        self.base_model = models.resnet50(weights=None)

        # The output features for ResNet-50 is 2048.
        num_ftrs = self.base_model.fc.in_features
        # We remove the final fully connected layer of the original ResNet to use it as a feature extractor.
        self.base_model.fc = nn.Identity()

        # Define the 5 separate classification heads, matching the names from the state_dict.
        self.orientation_classifier = nn.Linear(num_ftrs, 2)
        self.lobe_classifier = nn.Linear(num_ftrs, 3)
        self.longitudinal_classifier = nn.Linear(num_ftrs, 7)
        self.transverse_classifier = nn.Linear(num_ftrs, 5)
        self.isthmus_classifier = nn.Linear(num_ftrs, 2)

    def forward(self, x):
        """
        Defines the forward pass of the model.
        """
        # Pass input through the backbone to get the feature vector
        features = self.base_model(x)
        
        # Pass the extracted features through each of the classification heads
        orientation_out = self.orientation_classifier(features)
        lobe_out = self.lobe_classifier(features)
        longitudinal_out = self.longitudinal_classifier(features)
        transverse_out = self.transverse_classifier(features)
        isthmus_out = self.isthmus_classifier(features)

        # Return the outputs from all heads as a tuple
        return orientation_out, lobe_out, longitudinal_out, transverse_out, isthmus_out 