"""
甲状腺结节位置分类器训练脚本 v2
================================

主要功能:
- 训练5头多任务分类模型，预测甲状腺结节的空间位置
- 分类任务包括：切面方向、叶位、纵向位置、横向位置、峡部关系
- 基于ResNet50架构，针对不同切面使用条件损失计算
- 支持数据增强、早停、学习率调度和TensorBoard可视化

核心数据结构:
- ThyroidPositionDataset: 位置标签数据集类
- ThyroidPositionModel: 5头分类模型
- CONFIG: 训练配置参数
- Label mappings: 各个位置类别的标签映射

输入数据:
- train_dataset_crop.csv / val_dataset_crop.csv: 裁剪后的训练/验证数据
- label_maps_crop.json: 标签映射文件
- 图像文件路径

输出:
- 训练好的位置分类模型 (.pth)
- 混淆矩阵可视化 (.png)
- 训练损失图表
- TensorBoard日志

特殊逻辑:
- 条件损失：纵向位置只在纵切时计算，横向位置只在横切时计算
- 5个分类头：orientation, lobe, longitudinal, transverse, isthmus

作者: 项目团队
日期: 2024
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torchvision import models, transforms
from sklearn.metrics import accuracy_score, confusion_matrix
from PIL import Image
import json
from tqdm import tqdm
from torch.utils.tensorboard import SummaryWriter
import seaborn as sns
import datetime

# Set random seeds for reproducibility
torch.manual_seed(42)
np.random.seed(42)

# --- Configuration ---
CONFIG = {
    'img_root': '/Users/<USER>/Documents/images/nodules_115659/nodules_tr15_b26_40395', # IMPORTANT: Updated image root
    'img_size': 224,
    'batch_size': 32,
    'num_epochs': 30,
    'learning_rate': 0.0001,
    'weight_decay': 1e-5,
    'save_dir': 'models_crop', # New directory for cropped models
    'model_name': 'thyroid_position_model_crop_v1.pth', # New model name
    'label_map_file': 'dataset_crop/label_maps_crop.json', # Use the new label map
    'train_csv': 'dataset_crop/train_dataset_crop.csv',     # Use the new training data
    'val_csv': 'dataset_crop/val_dataset_crop.csv'          # Use the new validation data
}

os.makedirs(CONFIG['save_dir'], exist_ok=True)

# --- 1. Updated Custom Dataset for 5 Labels ---
class ThyroidPositionDataset(Dataset):
    def __init__(self, dataframe, image_root, transform=None):
        self.dataframe = dataframe
        # self.image_root is no longer needed for path joining, but kept for context.
        self.image_root = image_root
        self.transform = transform
        
    def __len__(self):
        return len(self.dataframe)
    
    def __getitem__(self, idx):
        row = self.dataframe.iloc[idx]
        
        # Get image
        # The CSV now contains the full, absolute path, so we use it directly.
        img_path = row['image_path']
        try:
            image = Image.open(img_path).convert('RGB')
            if self.transform:
                image = self.transform(image)
        except Exception as e:
            print(f"Warning: Error loading image {img_path}: {e}. Returning a black image.")
            image = torch.zeros((3, CONFIG['img_size'], CONFIG['img_size']))
        
        # Create target tensor with 5 labels
        labels = torch.tensor([
            row['lobe_label'],
            row['longitudinal_label'],
            row['transverse_label'],
            row['isthmus_label'],
            row['orientation_label']
        ], dtype=torch.long)
        
        return image, labels

# --- 2. Updated Multi-output Model for 5 Heads ---
class ThyroidPositionModel(nn.Module):
    def __init__(self, label_maps):
        super(ThyroidPositionModel, self).__init__()
        
        self.num_lobe_classes = len(label_maps['lobe'])
        self.num_longitudinal_classes = len(label_maps['longitudinal'])
        self.num_transverse_classes = len(label_maps['transverse'])
        self.num_isthmus_classes = len(label_maps['isthmus'])
        self.num_orientation_classes = len(label_maps['orientation'])
        
        self.base_model = models.resnet50(weights=models.ResNet50_Weights.DEFAULT)
        num_features = self.base_model.fc.in_features
        self.base_model.fc = nn.Identity()
        
        self.lobe_classifier = nn.Linear(num_features, self.num_lobe_classes)
        self.longitudinal_classifier = nn.Linear(num_features, self.num_longitudinal_classes)
        self.transverse_classifier = nn.Linear(num_features, self.num_transverse_classes)
        self.isthmus_classifier = nn.Linear(num_features, self.num_isthmus_classes)
        self.orientation_classifier = nn.Linear(num_features, self.num_orientation_classes) # New head
        
        # Add a Dropout layer to combat overfitting
        self.dropout = nn.Dropout(p=0.2)
        
    def forward(self, x):
        features = self.base_model(x)
        features = self.dropout(features) # Apply dropout before classifiers
        
        lobe_logits = self.lobe_classifier(features)
        longitudinal_logits = self.longitudinal_classifier(features)
        transverse_logits = self.transverse_classifier(features)
        isthmus_logits = self.isthmus_classifier(features)
        orientation_logits = self.orientation_classifier(features) # New head output
        
        return lobe_logits, longitudinal_logits, transverse_logits, isthmus_logits, orientation_logits

def plot_confusion_matrix(cm, class_names, task_name, save_dir):
    """
    Plots a confusion matrix using Seaborn's heatmap and saves it to a file.
    """
    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap=plt.cm.Blues, xticklabels=class_names, yticklabels=class_names)
    plt.title(f'Confusion Matrix - {task_name}')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plot_path = os.path.join(save_dir, f'confusion_matrix_{task_name}.png')
    plt.savefig(plot_path)
    plt.close()
    print(f"  Confusion matrix for '{task_name}' saved to {plot_path}")

# --- 3. Updated Training Function ---
def train_model(train_loader, val_loader, label_maps):
    # --- 1. Enhanced Device Selection (CUDA > MPS > CPU) ---
    if torch.cuda.is_available():
        device = torch.device("cuda")
    elif torch.backends.mps.is_available():
        device = torch.device("mps")
    else:
        device = torch.device("cpu")
    print(f"Using device: {device}")
    
    # --- 2. Initialize TensorBoard Writer with a unique, timestamped directory ---
    current_time = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
    log_dir = os.path.join('runs', 'thyroid_position_classifier', current_time)
    writer = SummaryWriter(log_dir=log_dir)
    print(f"TensorBoard logs will be saved to: {log_dir}")
    
    model = ThyroidPositionModel(label_maps).to(device)
    
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=CONFIG['learning_rate'], weight_decay=CONFIG['weight_decay'])
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=3, verbose=True)
    
    best_val_loss = float('inf')
    # --- MODIFICATION: Use dictionaries to store losses for easier logging ---
    train_losses_history = []
    val_losses_history = []

    print(f"Starting training for {CONFIG['num_epochs']} epochs...")
    
    for epoch in range(CONFIG['num_epochs']):
        # --- Training phase ---
        model.train()
        # --- MODIFICATION: Initialize dicts for running losses and sample counts ---
        train_running_losses = {'total': 0.0, 'orient': 0.0, 'lobe': 0.0, 'long': 0.0, 'trans': 0.0, 'isth': 0.0}
        train_running_counts = {'long': 0, 'trans': 0}

        progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{CONFIG['num_epochs']} [Train]")
        for inputs, labels in progress_bar:
            inputs = inputs.to(device)
            lobe_labels, long_labels, trans_labels, isth_labels, orient_labels = labels.T.to(device)
            
            optimizer.zero_grad()
            
            outputs = model(inputs)
            lobe_logits, long_logits, trans_logits, isth_logits, orient_logits = outputs
            
            # --- MODIFICATION: Refactored Loss Calculation for component-wise logging ---
            # Use masks for efficient, batch-wise conditional loss calculation
            long_mask = (orient_labels == 0)
            trans_mask = (orient_labels == 1)

            # 1. Calculate component losses
            loss_orient = criterion(orient_logits, orient_labels)
            loss_long = criterion(long_logits[long_mask], long_labels[long_mask]) if long_mask.any() else torch.tensor(0.0, device=device)
            loss_lobe = criterion(lobe_logits[trans_mask], lobe_labels[trans_mask]) if trans_mask.any() else torch.tensor(0.0, device=device)
            loss_trans = criterion(trans_logits[trans_mask], trans_labels[trans_mask]) if trans_mask.any() else torch.tensor(0.0, device=device)
            loss_isth = criterion(isth_logits[trans_mask], isth_labels[trans_mask]) if trans_mask.any() else torch.tensor(0.0, device=device)

            # 2. Combine for the final loss used in backpropagation
            loss = loss_orient + loss_long + loss_lobe + loss_trans + loss_isth

            loss.backward()
            optimizer.step()
            
            # 3. Update running totals for each component for logging
            train_running_losses['total'] += loss.item() * inputs.size(0)
            train_running_losses['orient'] += loss_orient.item() * inputs.size(0) # All samples have orientation
            if long_mask.any():
                train_running_losses['long'] += loss_long.item() * long_mask.sum().item()
                train_running_counts['long'] += long_mask.sum().item()
            if trans_mask.any():
                train_running_losses['lobe'] += loss_lobe.item() * trans_mask.sum().item()
                train_running_losses['trans'] += loss_trans.item() * trans_mask.sum().item()
                train_running_losses['isth'] += loss_isth.item() * trans_mask.sum().item()
                train_running_counts['trans'] += trans_mask.sum().item()

            progress_bar.set_postfix({'loss': loss.item()})
        
        # --- MODIFICATION: Calculate epoch loss for each component ---
        total_samples = len(train_loader.dataset)
        epoch_train_losses = {k: v / total_samples for k, v in train_running_losses.items()}
        # For conditional losses, normalize by the number of relevant samples to get the true average loss for that task
        if train_running_counts['long'] > 0:
            epoch_train_losses['long'] = train_running_losses['long'] / train_running_counts['long']
        if train_running_counts['trans'] > 0:
            epoch_train_losses['lobe'] = train_running_losses['lobe'] / train_running_counts['trans']
            epoch_train_losses['trans'] = train_running_losses['trans'] / train_running_counts['trans']
            epoch_train_losses['isth'] = train_running_losses['isth'] / train_running_counts['trans']

        # --- Validation phase ---
        model.eval()
        val_running_losses = {'total': 0.0, 'orient': 0.0, 'lobe': 0.0, 'long': 0.0, 'trans': 0.0, 'isth': 0.0}
        val_running_counts = {'long': 0, 'trans': 0}
        all_preds = {'lobe': [], 'long': [], 'trans': [], 'isth': [], 'orient': []}
        all_true = {'lobe': [], 'long': [], 'trans': [], 'isth': [], 'orient': []}
        
        with torch.no_grad():
            progress_bar = tqdm(val_loader, desc=f"Epoch {epoch+1}/{CONFIG['num_epochs']} [Val]")
            for inputs, labels in progress_bar:
                inputs = inputs.to(device)
                lobe_labels, long_labels, trans_labels, isth_labels, orient_labels = labels.T.to(device)
                
                outputs = model(inputs)
                lobe_logits, long_logits, trans_logits, isth_logits, orient_logits = outputs
                
                # --- MODIFICATION: Mirrored loss calculation logic for validation ---
                long_mask = (orient_labels == 0)
                trans_mask = (orient_labels == 1)

                loss_orient = criterion(orient_logits, orient_labels)
                loss_long = criterion(long_logits[long_mask], long_labels[long_mask]) if long_mask.any() else torch.tensor(0.0, device=device)
                loss_lobe = criterion(lobe_logits[trans_mask], lobe_labels[trans_mask]) if trans_mask.any() else torch.tensor(0.0, device=device)
                loss_trans = criterion(trans_logits[trans_mask], trans_labels[trans_mask]) if trans_mask.any() else torch.tensor(0.0, device=device)
                loss_isth = criterion(isth_logits[trans_mask], isth_labels[trans_mask]) if trans_mask.any() else torch.tensor(0.0, device=device)

                val_loss = loss_orient + loss_long + loss_lobe + loss_trans + loss_isth
                
                val_running_losses['total'] += val_loss.item() * inputs.size(0)
                val_running_losses['orient'] += loss_orient.item() * inputs.size(0)
                if long_mask.any():
                    val_running_losses['long'] += loss_long.item() * long_mask.sum().item()
                    val_running_counts['long'] += long_mask.sum().item()
                if trans_mask.any():
                    val_running_losses['lobe'] += loss_lobe.item() * trans_mask.sum().item()
                    val_running_losses['trans'] += loss_trans.item() * trans_mask.sum().item()
                    val_running_losses['isth'] += loss_isth.item() * trans_mask.sum().item()
                    val_running_counts['trans'] += trans_mask.sum().item()

                # --- Store predictions and labels for conditional metrics ---
                # Orientation is always relevant
                all_preds['orient'].extend(torch.max(orient_logits, 1)[1].cpu().numpy())
                all_true['orient'].extend(orient_labels.cpu().numpy())

                # Get predictions for all tasks
                lobe_preds_all = torch.max(lobe_logits, 1)[1]
                long_preds_all = torch.max(long_logits, 1)[1]
                trans_preds_all = torch.max(trans_logits, 1)[1]
                isth_preds_all = torch.max(isth_logits, 1)[1]

                # Append predictions and true labels only if they are relevant for the slice type
                for i in range(inputs.size(0)):
                    if orient_labels[i] == 0: # Longitudinal slice
                        all_preds['long'].append(long_preds_all[i].cpu().item())
                        all_true['long'].append(long_labels[i].cpu().item())
                    elif orient_labels[i] == 1: # Transverse slice
                        all_preds['lobe'].append(lobe_preds_all[i].cpu().item())
                        all_true['lobe'].append(lobe_labels[i].cpu().item())
                        all_preds['trans'].append(trans_preds_all[i].cpu().item())
                        all_true['trans'].append(trans_labels[i].cpu().item())
                        all_preds['isth'].append(isth_preds_all[i].cpu().item())
                        all_true['isth'].append(isth_labels[i].cpu().item())

        # --- MODIFICATION: Calculate epoch validation losses per component ---
        total_val_samples = len(val_loader.dataset)
        epoch_val_losses = {k: v / total_val_samples for k, v in val_running_losses.items()}
        if val_running_counts['long'] > 0:
            epoch_val_losses['long'] = val_running_losses['long'] / val_running_counts['long']
        if val_running_counts['trans'] > 0:
            epoch_val_losses['lobe'] = val_running_losses['lobe'] / val_running_counts['trans']
            epoch_val_losses['trans'] = val_running_losses['trans'] / val_running_counts['trans']
            epoch_val_losses['isth'] = val_running_losses['isth'] / val_running_counts['trans']

        # --- For backward compatibility with scheduler and best model saving ---
        epoch_val_loss = epoch_val_losses['total']

        # Calculate accuracy for each task based on relevant samples
        # Add a small epsilon to avoid division by zero if a category has no samples in a batch
        acc_lobe = accuracy_score(all_true['lobe'], all_preds['lobe']) if len(all_true['lobe']) > 0 else 0.0
        acc_long = accuracy_score(all_true['long'], all_preds['long']) if len(all_true['long']) > 0 else 0.0
        acc_trans = accuracy_score(all_true['trans'], all_preds['trans']) if len(all_true['trans']) > 0 else 0.0
        acc_isth = accuracy_score(all_true['isth'], all_preds['isth']) if len(all_true['isth']) > 0 else 0.0
        acc_orient = accuracy_score(all_true['orient'], all_preds['orient']) if len(all_true['orient']) > 0 else 0.0
        
        print(f"\nEpoch {epoch+1}/{CONFIG['num_epochs']}: Train Loss: {epoch_train_losses['total']:.4f}, Val Loss: {epoch_val_loss:.4f}")
        print(f"  Accuracies -> Orient: {acc_orient:.4f}, Lobe: {acc_lobe:.4f}, Long: {acc_long:.4f}, Trans: {acc_trans:.4f}, Isth: {acc_isth:.4f}")
        
        # --- MODIFICATION: Log all loss components to TensorBoard ---
        writer.add_scalars('Loss/Train_Total', {'train': epoch_train_losses['total']}, epoch)
        writer.add_scalars('Loss/Val_Total', {'val': epoch_val_loss}, epoch)
        
        writer.add_scalars('Loss/Components_Train', {
            '1_Orientation': epoch_train_losses['orient'],
            '2_Lobe': epoch_train_losses['lobe'],
            '3_Longitudinal': epoch_train_losses['long'],
            '4_Transverse': epoch_train_losses['trans'],
            '5_Isthmus': epoch_train_losses['isth'],
        }, epoch)

        writer.add_scalars('Loss/Components_Val', {
            '1_Orientation': epoch_val_losses['orient'],
            '2_Lobe': epoch_val_losses['lobe'],
            '3_Longitudinal': epoch_val_losses['long'],
            '4_Transverse': epoch_val_losses['trans'],
            '5_Isthmus': epoch_val_losses['isth'],
        }, epoch)

        writer.add_scalars('Accuracy', {
            'Orientation': acc_orient,
            'Lobe': acc_lobe,
            'Longitudinal': acc_long,
            'Transverse': acc_trans,
            'Isthmus': acc_isth,
        }, epoch)
        writer.add_scalar('Learning Rate', optimizer.param_groups[0]['lr'], epoch)
        
        scheduler.step(epoch_val_loss)
        
        # Save the best model based on validation loss
        if epoch_val_loss < best_val_loss:
            best_val_loss = epoch_val_loss
            model_path = os.path.join(CONFIG['save_dir'], CONFIG['model_name'])
            torch.save(model.state_dict(), model_path)
            print(f"  Model improved and saved to {model_path}")
            
        # --- 3. Save a checkpoint every 10 epochs ---
        if (epoch + 1) % 10 == 0:
            checkpoint_path = os.path.join(CONFIG['save_dir'], f"thyroid_model_epoch_{epoch+1}.pth")
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': epoch_val_loss,
            }, checkpoint_path)
            print(f"  Checkpoint saved to {checkpoint_path}")

        # --- MODIFICATION: Store epoch losses for plotting ---
        train_losses_history.append(epoch_train_losses)
        val_losses_history.append(epoch_val_losses)
    
    # --- Final Evaluation and Confusion Matrix Plotting ---
    print("\nTraining finished. Performing final evaluation on the best model...")
    best_model_path = os.path.join(CONFIG['save_dir'], CONFIG['model_name'])
    if not os.path.exists(best_model_path):
        print("Could not find the best model file. Skipping final evaluation.")
        return

    model.load_state_dict(torch.load(best_model_path, map_location=device))
    model.eval()

    final_preds = {'lobe': [], 'long': [], 'trans': [], 'isth': [], 'orient': []}
    final_true = {'lobe': [], 'long': [], 'trans': [], 'isth': [], 'orient': []}

    with torch.no_grad():
        for inputs, labels in tqdm(val_loader, desc="Final Evaluation"):
            inputs = inputs.to(device)
            lobe_labels, long_labels, trans_labels, isth_labels, orient_labels = labels.T.to(device)
            
            outputs = model(inputs)
            lobe_logits, long_logits, trans_logits, isth_logits, orient_logits = outputs
            
            # --- Correctly gather predictions based on conditional logic ---
            # Get predictions for all tasks first
            lobe_preds_all = torch.max(lobe_logits, 1)[1]
            long_preds_all = torch.max(long_logits, 1)[1]
            trans_preds_all = torch.max(trans_logits, 1)[1]
            isth_preds_all = torch.max(isth_logits, 1)[1]
            orient_preds_all = torch.max(orient_logits, 1)[1]

            # Orientation is always relevant
            final_true['orient'].extend(orient_labels.cpu().numpy())
            final_preds['orient'].extend(orient_preds_all.cpu().numpy())

            # Append predictions and true labels only if they are relevant for the slice type
            for i in range(inputs.size(0)):
                if orient_labels[i] == 0: # Longitudinal slice
                    final_preds['long'].append(long_preds_all[i].cpu().item())
                    final_true['long'].append(long_labels[i].cpu().item())
                elif orient_labels[i] == 1: # Transverse slice
                    final_preds['lobe'].append(lobe_preds_all[i].cpu().item())
                    final_true['lobe'].append(lobe_labels[i].cpu().item())
                    final_preds['trans'].append(trans_preds_all[i].cpu().item())
                    final_true['trans'].append(trans_labels[i].cpu().item())
                    final_preds['isth'].append(isth_preds_all[i].cpu().item())
                    final_true['isth'].append(isth_labels[i].cpu().item())

    # Map task keys to keys in label_maps and provide manual maps where needed
    task_to_map_key = {
        'orient': ('orientation', {0: 'Longitudinal', 1: 'Transverse'}),
        'lobe': ('lobe', None),
        'long': ('longitudinal', None),
        'trans': ('transverse', None),
        'isth': ('isthmus', None)
    }

    print("\nGenerating confusion matrices...")
    for task_name, (map_key, manual_class_map) in task_to_map_key.items():
        if not final_true[task_name]:
            print(f"  Skipping confusion matrix for '{task_name}': No relevant samples found in validation set.")
            continue

        if manual_class_map:
            # Use the manually provided class names
            class_names = list(manual_class_map.values())
        else:
            # Invert the label map to get class names from indices
            inverted_map = {v: k for k, v in label_maps[map_key].items()}
            class_names = [inverted_map[i] for i in sorted(inverted_map.keys())]

        cm = confusion_matrix(final_true[task_name], final_preds[task_name])
        plot_confusion_matrix(cm, class_names, task_name, CONFIG['save_dir'])

    # Close the TensorBoard writer
    writer.close()
    
    # Plotting loss
    plt.figure(figsize=(10, 5))
    plt.plot([e['total'] for e in train_losses_history], label='Training Loss')
    plt.plot([e['total'] for e in val_losses_history], label='Validation Loss')
    plt.xlabel('Epoch'); plt.ylabel('Loss'); plt.title('Training and Validation Loss'); plt.legend(); plt.grid(True)
    plt.savefig(os.path.join(CONFIG['save_dir'], 'loss_plot_5_heads.png'))
    
    return model

# --- 4. Simplified Main Execution Block ---
def main():
    try:
        with open(CONFIG['label_map_file'], 'r') as f:
            label_maps = json.load(f)
        train_df = pd.read_csv(CONFIG['train_csv'])
        val_df = pd.read_csv(CONFIG['val_csv'])
    except FileNotFoundError as e:
        print(f"Error: Missing required file: {e.filename}")
        print("Please run 'process_dataset.py' successfully before running this script.")
        return

    train_transform = transforms.Compose([
        transforms.Resize((CONFIG['img_size'], CONFIG['img_size'])),
        transforms.RandomAffine(degrees=10, translate=(0.15, 0.15), scale=(0.8, 1.2)),
        transforms.ColorJitter(brightness=0.2, contrast=0.2),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    val_transform = transforms.Compose([
        transforms.Resize((CONFIG['img_size'], CONFIG['img_size'])),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # Create datasets
    train_dataset = ThyroidPositionDataset(train_df, CONFIG['img_root'], transform=train_transform)
    val_dataset = ThyroidPositionDataset(val_df, CONFIG['img_root'], transform=val_transform)
    
    # DEBUG: Set num_workers=0 to simplify debugging DataLoaders.
    # This runs data loading in the main process and provides clearer error messages.
    train_loader = DataLoader(train_dataset, batch_size=CONFIG['batch_size'], shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=CONFIG['batch_size'], shuffle=False, num_workers=0)
    
    # Train the model
    model = train_model(train_loader, val_loader, label_maps)
    
    print("Training complete!")

if __name__ == "__main__":
    main() 