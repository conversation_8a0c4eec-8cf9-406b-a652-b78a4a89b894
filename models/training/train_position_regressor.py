"""
甲状腺结节3D坐标回归模型训练脚本
================================

主要功能:
- 训练回归模型预测甲状腺结节的3D空间坐标 (RL, UD, FB)
- RL: 右左坐标 (0=右, 1=左)
- UD: 上下坐标 (0=上, 1=下) 
- FB: 前后坐标 (0=前, 1=后)
- 基于ResNet50 + Sigmoid激活，输出范围[0,1]
- 支持缺失值处理和掩码损失计算

核心数据结构:
- ThyroidCoordinateDataset: 3D坐标数据集类
- ThyroidPositionRegressor: 回归模型（ResNet50 + 3路输出）
- masked_mse_loss: 处理NaN值的MSE损失函数
- CONFIG: 训练配置参数

输入数据:
- train_reg.csv / val_reg.csv: 回归训练/验证数据
- image_path: 图像文件路径
- rl_label, ud_label, fb_label: 3D坐标标签

输出:
- 训练好的回归模型 (.pth)
- 早停检查点文件
- TensorBoard训练指标
- MAE, RMSE, 准确度评估结果

特殊功能:
- 早停机制防止过拟合
- 支持训练恢复
- 处理坐标缺失值（NaN）
- 多种回归评估指标

作者: 项目团队
日期: 2024
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torchvision import models, transforms
from PIL import Image
from tqdm import tqdm
from torch.utils.tensorboard import SummaryWriter
import datetime
from sklearn.metrics import mean_absolute_error, mean_squared_error

# Set random seeds for reproducibility
torch.manual_seed(42)
np.random.seed(42)

# --- Configuration ---
CONFIG = {
    'img_size': 224,
    'batch_size': 32,
    'num_epochs': 50, # Regression might need more epochs
    'learning_rate': 0.0001,
    'weight_decay': 1e-5,
    'save_dir': 'models_reg',
    'model_name': 'thyroid_position_regressor_v1.pth',
    'train_csv': 'dataset_reg/train_reg.csv',
    'val_csv': 'dataset_reg/val_reg.csv',
    # --- NEW: Early Stopping and Resuming ---
    'early_stopping_patience': 10, # Stop if val_loss doesn't improve for 10 epochs
    'checkpoint_to_resume': ''  # Set path to a checkpoint file to resume training
}

os.makedirs(CONFIG['save_dir'], exist_ok=True)

# --- 1. Custom Dataset for Regression ---
class ThyroidCoordinateDataset(Dataset):
    def __init__(self, dataframe, transform=None):
        self.dataframe = dataframe
        self.transform = transform
        
    def __len__(self):
        return len(self.dataframe)
    
    def __getitem__(self, idx):
        row = self.dataframe.iloc[idx]
        
        # Image path is absolute
        img_path = row['image_path']
        try:
            image = Image.open(img_path).convert('RGB')
            if self.transform:
                image = self.transform(image)
        except Exception as e:
            print(f"Warning: Error loading image {img_path}: {e}. Returning a black image.")
            image = torch.zeros((3, CONFIG['img_size'], CONFIG['img_size']))
        
        # Create target tensor with RL, UD, FB coordinates, allowing NaNs
        labels = torch.tensor([
            row['rl_label'],
            row['ud_label'],
            row['fb_label']
        ], dtype=torch.float32)
        
        return image, labels

# --- 2. Regression Model ---
class ThyroidPositionRegressor(nn.Module):
    def __init__(self):
        super(ThyroidPositionRegressor, self).__init__()
        
        self.base_model = models.resnet50(weights=models.ResNet50_Weights.DEFAULT)
        num_features = self.base_model.fc.in_features
        self.base_model.fc = nn.Identity()
        
        self.regressor = nn.Sequential(
            nn.Dropout(p=0.5),
            nn.Linear(num_features, 3),
            nn.Sigmoid() # Constrain output to [0, 1]
        )
        
    def forward(self, x):
        features = self.base_model(x)
        coordinates = self.regressor(features)
        return coordinates

# --- 3. Masked Loss Function ---
def masked_mse_loss(predictions, targets):
    """Calculates MSE loss only on non-NaN targets."""
    mask = ~torch.isnan(targets)
    if not mask.any():
        return torch.tensor(0.0, device=predictions.device, requires_grad=True)
    
    # Apply mask to both predictions and targets before calculating loss
    masked_preds = torch.masked_select(predictions, mask)
    masked_targets = torch.masked_select(targets, mask)
    
    loss = nn.MSELoss()(masked_preds, masked_targets)
    return loss

def masked_mse_loss_components(predictions, targets):
    """Calculates MSE for each component (RL, UD, FB) and returns them separately."""
    losses = {}
    components = ['rl', 'ud', 'fb']
    
    for i, comp in enumerate(components):
        comp_targets = targets[:, i]
        comp_preds = predictions[:, i]
        mask = ~torch.isnan(comp_targets)
        
        if mask.any():
            masked_preds = torch.masked_select(comp_preds, mask)
            masked_targets = torch.masked_select(comp_targets, mask)
            losses[comp] = nn.MSELoss()(masked_preds, masked_targets)
        else:
            losses[comp] = torch.tensor(0.0, device=predictions.device)
            
    return losses

# --- NEW: Function to calculate regression metrics ---
def calculate_regression_metrics(y_true, y_pred, tolerance=0.1):
    """Calculates MAE, RMSE, and Accuracy within a tolerance."""
    if len(y_true) == 0:
        return {'mae': 0, 'rmse': 0, 'acc_tol': 0}

    y_true = np.array(y_true)
    y_pred = np.array(y_pred)

    mae = mean_absolute_error(y_true, y_pred)
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    
    # Calculate accuracy within the given tolerance
    acc_tol = np.mean(np.abs(y_true - y_pred) <= tolerance)
    
    return {'mae': mae, 'rmse': rmse, 'acc_tol': acc_tol}

# --- 4. Training Function ---
def train_model(train_loader, val_loader):
    if torch.cuda.is_available():
        device = torch.device("cuda")
    elif torch.backends.mps.is_available():
        device = torch.device("mps")
    else:
        device = torch.device("cpu")
    print(f"Using device: {device}")
    
    current_time = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
    log_dir = os.path.join('runs', 'thyroid_position_regressor', current_time)
    writer = SummaryWriter(log_dir=log_dir)
    print(f"TensorBoard logs will be saved to: {log_dir}")
    
    model = ThyroidPositionRegressor().to(device)
    optimizer = optim.Adam(model.parameters(), lr=CONFIG['learning_rate'], weight_decay=CONFIG['weight_decay'])
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.2, patience=5, verbose=True)
    
    # --- MODIFICATION: Logic for resuming training ---
    start_epoch = 0
    best_val_loss = float('inf')
    
    if CONFIG['checkpoint_to_resume'] and os.path.exists(CONFIG['checkpoint_to_resume']):
        print(f"Resuming training from checkpoint: {CONFIG['checkpoint_to_resume']}")
        checkpoint = torch.load(CONFIG['checkpoint_to_resume'], map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        start_epoch = checkpoint['epoch'] + 1
        best_val_loss = checkpoint['best_val_loss']
        print(f"Resumed from epoch {start_epoch}. Best validation loss so far: {best_val_loss:.6f}")

    # --- MODIFICATION: Early stopping setup ---
    epochs_no_improve = 0

    for epoch in range(start_epoch, CONFIG['num_epochs']):
        # --- Training phase ---
        model.train()
        train_running_losses = {'total': 0.0, 'rl': 0.0, 'ud': 0.0, 'fb': 0.0}
        
        progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{CONFIG['num_epochs']} [Train]")
        for inputs, targets in progress_bar:
            inputs, targets = inputs.to(device), targets.to(device)
            
            optimizer.zero_grad()
            
            predictions = model(inputs)
            
            loss = masked_mse_loss(predictions, targets)
            
            if loss.requires_grad:
                loss.backward()
                optimizer.step()
            
            with torch.no_grad():
                comp_losses = masked_mse_loss_components(predictions, targets)
                train_running_losses['total'] += loss.item() * inputs.size(0)
                train_running_losses['rl'] += comp_losses['rl'].item() * inputs.size(0)
                train_running_losses['ud'] += comp_losses['ud'].item() * inputs.size(0)
                train_running_losses['fb'] += comp_losses['fb'].item() * inputs.size(0)

            progress_bar.set_postfix({'loss': loss.item()})
        
        epoch_train_loss = train_running_losses['total'] / len(train_loader.dataset)
        epoch_train_comp_losses = {k: v / len(train_loader.dataset) for k,v in train_running_losses.items()}

        # --- Validation phase ---
        model.eval()
        val_running_losses = {'total': 0.0, 'rl': 0.0, 'ud': 0.0, 'fb': 0.0}
        all_preds = {'rl': [], 'ud': [], 'fb': []}
        all_targets = {'rl': [], 'ud': [], 'fb': []}
        
        with torch.no_grad():
            for inputs, targets in val_loader:
                inputs, targets = inputs.to(device), targets.to(device)
                predictions = model(inputs)
                
                loss = masked_mse_loss(predictions, targets)
                comp_losses = masked_mse_loss_components(predictions, targets)
                
                val_running_losses['total'] += loss.item() * inputs.size(0)
                val_running_losses['rl'] += comp_losses['rl'].item() * inputs.size(0)
                val_running_losses['ud'] += comp_losses['ud'].item() * inputs.size(0)
                val_running_losses['fb'] += comp_losses['fb'].item() * inputs.size(0)

                for i, comp in enumerate(['rl', 'ud', 'fb']):
                    comp_targets = targets[:, i]
                    comp_preds = predictions[:, i]
                    mask = ~torch.isnan(comp_targets)
                    if mask.any():
                        all_targets[comp].extend(comp_targets[mask].cpu().numpy())
                        all_preds[comp].extend(comp_preds[mask].cpu().numpy())
        
        epoch_val_loss = val_running_losses['total'] / len(val_loader.dataset)
        epoch_val_comp_losses = {k: v / len(val_loader.dataset) for k,v in val_running_losses.items()}
        
        val_metrics = {}
        print("  --- Validation Metrics ---")
        for comp in ['rl', 'ud', 'fb']:
            if all_targets[comp]:
                metrics = calculate_regression_metrics(all_targets[comp], all_preds[comp])
                val_metrics[comp] = metrics
                print(f"    {comp.upper()} Axis -> MAE: {metrics['mae']:.4f} | RMSE: {metrics['rmse']:.4f} | Acc@0.1: {metrics['acc_tol']:.2%}")
            else:
                val_metrics[comp] = {'mae': 0, 'rmse': 0, 'acc_tol': 0}

        print(f"\nEpoch {epoch+1}/{CONFIG['num_epochs']}: Train Loss: {epoch_train_loss:.6f}, Val Loss: {epoch_val_loss:.6f}")
        
        # --- Logging to TensorBoard ---
        writer.add_scalars('Loss/Total', {'train': epoch_train_loss, 'val': epoch_val_loss}, epoch)
        writer.add_scalars('Loss/Components_Train', {
            'RL': epoch_train_comp_losses['rl'],
            'UD': epoch_train_comp_losses['ud'],
            'FB': epoch_train_comp_losses['fb'],
        }, epoch)
        writer.add_scalars('Loss/Components_Val', {
            'RL': epoch_val_comp_losses['rl'],
            'UD': epoch_val_comp_losses['ud'],
            'FB': epoch_val_comp_losses['fb'],
        }, epoch)
        # --- MODIFICATION: Log new metrics to TensorBoard ---
        writer.add_scalars('Metrics/MAE', {
            'RL': val_metrics['rl']['mae'],
            'UD': val_metrics['ud']['mae'],
            'FB': val_metrics['fb']['mae'],
        }, epoch)
        writer.add_scalars('Metrics/RMSE', {
            'RL': val_metrics['rl']['rmse'],
            'UD': val_metrics['ud']['rmse'],
            'FB': val_metrics['fb']['rmse'],
        }, epoch)
        writer.add_scalars('Metrics/Accuracy_Tolerance_0.1', {
            'RL': val_metrics['rl']['acc_tol'],
            'UD': val_metrics['ud']['acc_tol'],
            'FB': val_metrics['fb']['acc_tol'],
        }, epoch)

        writer.add_scalar('Learning Rate', optimizer.param_groups[0]['lr'], epoch)
        
        scheduler.step(epoch_val_loss)
        
        # --- MODIFICATION: Checkpointing and Early Stopping ---
        # Always save the latest state for resuming
        latest_checkpoint_path = os.path.join(CONFIG['save_dir'], 'latest_checkpoint.pth')
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'best_val_loss': best_val_loss,
        }, latest_checkpoint_path)

        # Save the best model and check for early stopping
        if epoch_val_loss < best_val_loss:
            best_val_loss = epoch_val_loss
            epochs_no_improve = 0
            best_model_path = os.path.join(CONFIG['save_dir'], CONFIG['model_name'])
            torch.save(model.state_dict(), best_model_path)
            print(f"  Validation loss improved. Best model saved to {best_model_path}")
        else:
            epochs_no_improve += 1
            print(f"  Validation loss did not improve for {epochs_no_improve} epoch(s).")

        if epochs_no_improve >= CONFIG['early_stopping_patience']:
            print(f"\nEarly stopping triggered after {epochs_no_improve} epochs without improvement.")
            break
            
    writer.close()
    return model

# --- 5. Main Execution Block ---
def main():
    try:
        train_df = pd.read_csv(CONFIG['train_csv'])
        val_df = pd.read_csv(CONFIG['val_csv'])
    except FileNotFoundError as e:
        print(f"Error: Missing required file: {e.filename}", file=sys.stderr)
        return

    # --- MODIFICATION: Lighter and more varied augmentations ---
    train_transform = transforms.Compose([
        transforms.Resize((CONFIG['img_size'], CONFIG['img_size'])),
        transforms.RandomAffine(degrees=10, translate=(0.05, 0.05), scale=(0.95, 1.05)),
        transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    val_transform = transforms.Compose([
        transforms.Resize((CONFIG['img_size'], CONFIG['img_size'])),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    train_dataset = ThyroidCoordinateDataset(train_df, transform=train_transform)
    val_dataset = ThyroidCoordinateDataset(val_df, transform=val_transform)
    
    train_loader = DataLoader(train_dataset, batch_size=CONFIG['batch_size'], shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=CONFIG['batch_size'], shuffle=False, num_workers=0)
    
    train_model(train_loader, val_loader)
    
    print("Training complete!")

if __name__ == "__main__":
    main() 