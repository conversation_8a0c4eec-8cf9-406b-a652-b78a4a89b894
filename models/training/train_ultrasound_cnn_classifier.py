"""
甲状腺超声图像CNN分类器训练脚本
==================================

主要功能:
- 训练多输出CNN模型分类甲状腺结节的7个特征：病理良恶性(bom)、TI-RADS等级、成分、回声、纵横比、边缘、钙化
- 基于ResNet50架构，使用多头分类器
- 支持中英文双语标签和可视化
- 包含数据预处理、训练、验证和结果分析

核心数据结构:
- UltrasoundCNNDataset: 自定义数据集类，处理图像和多标签数据
- UltrasoundCNNModel: 多输出CNN模型，7个分类头
- FEATURE_META_BILINGUAL: 双语特征标签映射字典
- CONFIG: 训练配置参数字典

输入数据:
- annotations_nodules_dataset_v16.csv: 结节标注数据
- nodule_image_path列指向的图像文件

输出:
- 训练好的模型文件 (.pth)
- 标签映射文件 (.json)  
- 混淆矩阵图像文件 (.png)
- TensorBoard训练日志

作者: 项目团队
日期: 2024
"""

import os
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torchvision import models, transforms
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score, confusion_matrix, classification_report
from PIL import Image
import json
from tqdm import tqdm
import datetime
import seaborn as sns
import matplotlib.pyplot as plt
from torch.utils.tensorboard import SummaryWriter

# --- Font Configuration for Chinese Characters ---
try:
    plt.rcParams['font.sans-serif'] = ['PingFang SC', 'SimHei', 'Heiti TC']
    plt.rcParams['axes.unicode_minus'] = False  # To display negative signs correctly
    print("Chinese font set successfully for training script.")
except Exception as e:
    print(f"Could not set Chinese font, will use default. Warning: {e}")

# --- Bilingual Feature Meta Information ---
FEATURE_META_BILINGUAL = {
    'ti_rads': {'0': 'TI-RADS 1', '1': 'TI-RADS 2', '2': 'TI-RADS 3', '3': 'TI-RADS 4', '4': 'TI-RADS 5', 'MISSING': '未提供 (Missing)'},
    'std_composition': {'囊性': '囊性 (Cystic)', '海绵样': '海绵样 (Spongiform)', '囊实性': '囊实性 (Cystic-Solid)', '实性': '实性 (Solid)', 'MISSING': '未提供 (Missing)'},
    'std_echo': {'无回声': '无回声 (Anechoic)', '高回声': '高回声 (Hyperechoic)', '等回声': '等回声 (Isoechoic)', '低回声': '低回声 (Hypoechoic)', '极低回声': '极低回声 (Very-Hypoechoic)', '不均匀': '不均匀 (Uneven)', '强回声': '强回声 (Strongly-Hyperechoic)', 'MISSING': '未提供 (Missing)'},
    'std_margin': {'光滑': '光滑 (Smooth)', '分叶': '分叶 (Lobulated)', '不清': '不清 (Indistinct)', '不规则': '不规则 (Irregular)', '外侵': '外侵 (Extracapsular-Extension)', 'MISSING': '未提供 (Missing)'},
    'std_foci': {'大彗星尾': '大彗星尾 (Comet-Tail Artifact)', '粗大钙化': '粗大钙化 (Macrocalcification)', '边缘钙化': '边缘钙化 (Rim Calcification)', '点状强回声': '点状强回声 (Punctate Echogenic Foci)', '大彗星尾,点状强回声': '大彗星尾,点状强回声 (Comet-Tail & Punctate)', '粗大钙化,点状强回声': '粗大钙化,点状强回声 (Macro & Punctate)', '边缘钙化,点状强回声': '边缘钙化,点状强回声 (Rim & Punctate)', '粗大钙化,边缘钙化': '粗大钙化,边缘钙化 (Macro & Rim)', 'MISSING': '未提供 (Missing)'},
    'std_ratio': {'0': '宽度>高度 (Width > Height)', '1': '高度>宽度 (Height > Width)', 'MISSING': '未提供 (Missing)'},
    'bom': {'0': '良性 (Benign)', '1': '恶性 (Malignant)', 'MISSING': '未提供 (Missing)'}
}

# --- Configuration ---
CONFIG = {
    'eval_only': True,
    'annotations_csv_path': 'annotations_nodules_dataset_v16.csv',
    'img_size': 224,
    'batch_size': 32,
    'num_epochs': 30,
    'learning_rate': 0.0001,
    'weight_decay': 1e-5,
    'save_dir': 'models_cnn_features_v16',
    'symlink_dir': 'dataset_v16',
    'model_name': 'ultrasound_cnn_model_v16.pth',
    'label_maps_name': 'cnn_features_label_maps_v16.json',
    'missing_indices_name': 'cnn_features_missing_indices_v16.json',
}

os.makedirs(CONFIG['save_dir'], exist_ok=True)
torch.manual_seed(42)
np.random.seed(42)

# --- NEW: Function to create symlinks for easy data inspection ---
def create_symlinks(dataframe, set_name, base_dir):
    """Creates symlinks to image files for a given dataset split."""
    symlink_path = os.path.join(base_dir, set_name, 'images')
    os.makedirs(symlink_path, exist_ok=True)
    print(f"Creating symlinks for '{set_name}' set in '{symlink_path}'...")
    
    for _, row in tqdm(dataframe.iterrows(), total=len(dataframe), desc=f"Linking {set_name} images"):
        src_path = row['nodule_image_path']
        # Use sop_uid for a unique filename
        dst_filename = f"{row['sop_uid']}.jpg"
        dst_path = os.path.join(symlink_path, dst_filename)
        
        # Ensure the source file exists and avoid broken links
        if os.path.exists(src_path) and not os.path.exists(dst_path):
            os.symlink(src_path, dst_path)
            
    print(f"Symlink creation for '{set_name}' complete.")

# --- 1. Data Preparation ---
def prepare_data(config):
    print("Preparing data...")
    df = pd.read_csv(config['annotations_csv_path'])

    if '病理良恶性' in df.columns:
        df['bom'] = df['病理良恶性']
    
    target_cols = ['bom', 'ti_rads', 'std_composition', 'std_echo', 'std_ratio', 'std_margin', 'std_foci']
    
    nan_placeholder = 'MISSING'
    missing_value_indices = {}
    label_maps = {}

    for col in target_cols:
        if df[col].dtype == 'object':
            df[col] = df[col].fillna(nan_placeholder)
        else:
            df[col] = pd.to_numeric(df[col], errors='coerce').astype(str).fillna(nan_placeholder)
        
        if col == 'ti_rads':
            df[col] = df[col].apply(lambda x: str(int(float(x) - 1)) if x != nan_placeholder and x != 'nan' else nan_placeholder)

        le = LabelEncoder()
        df[col] = le.fit_transform(df[col])
        label_maps[col] = {i: str(label) for i, label in enumerate(le.classes_)}
        
        missing_class_list = np.where(le.classes_ == nan_placeholder)[0]
        missing_value_indices[col] = int(missing_class_list[0]) if len(missing_class_list) > 0 else -1

    # Save maps
    with open(os.path.join(config['save_dir'], config['label_maps_name']), 'w') as f:
        json.dump(label_maps, f, indent=4)
    with open(os.path.join(config['save_dir'], config['missing_indices_name']), 'w') as f:
        json.dump(missing_value_indices, f, indent=4)
        
    print(f"Label maps and missing value indices saved to {config['save_dir']}")
    
    # Filter out rows where the nodule image does not exist
    initial_count = len(df)
    df = df[df['nodule_image_path'].apply(lambda x: isinstance(x, str) and os.path.exists(x))]
    print(f"Filtered out {initial_count - len(df)} rows with non-existent image paths.")

    train_df, val_df = train_test_split(df, test_size=0.2, random_state=42)
    
    return train_df, val_df, target_cols, label_maps, missing_value_indices

# --- 2. Custom Dataset ---
class UltrasoundCNNDataset(Dataset):
    def __init__(self, dataframe, target_cols, transform=None):
        self.dataframe = dataframe
        self.transform = transform
        self.target_cols = target_cols
    
    def __len__(self):
        return len(self.dataframe)
    
    def __getitem__(self, idx):
        row = self.dataframe.iloc[idx]
        img_path = row['nodule_image_path']
        
        try:
            image = Image.open(img_path).convert('RGB')
            if self.transform:
                image = self.transform(image)
        except Exception as e:
            print(f"Warning: Error loading image {img_path}: {e}. Returning a black image.")
            image = torch.zeros((3, CONFIG['img_size'], CONFIG['img_size']))
            
        labels = torch.tensor(row[self.target_cols].values.astype(np.int64), dtype=torch.long)
        return image, labels

# --- 3. Multi-output CNN Model ---
class UltrasoundCNNModel(nn.Module):
    def __init__(self, label_maps):
        super(UltrasoundCNNModel, self).__init__()
        self.base_model = models.resnet50(weights=models.ResNet50_Weights.DEFAULT)
        num_features = self.base_model.fc.in_features
        self.base_model.fc = nn.Identity()
        
        self.dropout = nn.Dropout(0.5)
        
        self.classifiers = nn.ModuleDict({
            name: nn.Linear(num_features, len(classes)) for name, classes in label_maps.items()
        })
        
    def forward(self, x):
        features = self.base_model(x)
        features = self.dropout(features)
        outputs = {name: classifier(features) for name, classifier in self.classifiers.items()}
        return outputs

def plot_confusion_matrix_func(cm, class_names, task_name, save_dir):
    """Plots and saves a confusion matrix with bilingual labels."""
    plt.figure(figsize=(12, 10))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=class_names, yticklabels=class_names, annot_kws={"size": 8})
    english_task_name = task_name.replace('_', ' ').title()
    plt.title(f'混淆矩阵 - {task_name} (Confusion Matrix - {english_task_name})', fontsize=16)
    plt.ylabel('真实标签 (True Label)', fontsize=12)
    plt.xlabel('预测标签 (Predicted Label)', fontsize=12)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plot_path = os.path.join(save_dir, f'confusion_matrix_{task_name}.png')
    plt.savefig(plot_path)
    plt.close()

# --- 4. Training and Evaluation Loop ---
def run_training_and_validation(model, train_loader, val_loader, config, target_cols, missing_indices):
    device = torch.device("cuda" if torch.cuda.is_available() else "mps" if torch.backends.mps.is_available() else "cpu")
    model.to(device)
    print(f"Using device: {device}")
    
    writer = SummaryWriter(log_dir=os.path.join('runs', 'ultrasound_cnn_classifier_v16', datetime.datetime.now().strftime("%Y%m%d-%H%M%S")))
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=config['learning_rate'], weight_decay=config['weight_decay'])
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=3, factor=0.5, verbose=True)
    
    best_val_loss = float('inf')

    for epoch in range(config['num_epochs']):
        model.train()
        total_train_loss = 0
        for inputs, labels in tqdm(train_loader, desc=f"Epoch {epoch+1}/{config['num_epochs']} [Train]"):
            inputs, labels = inputs.to(device), labels.to(device)
            optimizer.zero_grad()
            outputs = model(inputs)
            
            loss = 0
            for i, name in enumerate(target_cols):
                mask = labels[:, i] != missing_indices[name]
                if mask.any():
                    loss += criterion(outputs[name][mask], labels[:, i][mask])
            
            if hasattr(loss, 'backward'):
                loss.backward()
                optimizer.step()
                total_train_loss += loss.item()

        model.eval()
        total_val_loss = 0
        all_preds = {name: [] for name in target_cols}
        all_true = {name: [] for name in target_cols}
        with torch.no_grad():
            for inputs, labels in tqdm(val_loader, desc=f"Epoch {epoch+1}/{config['num_epochs']} [Val]"):
                inputs, labels = inputs.to(device), labels.to(device)
                outputs = model(inputs)
                loss = 0
                for i, name in enumerate(target_cols):
                    mask = labels[:, i] != missing_indices[name]
                    if mask.any():
                        loss += criterion(outputs[name][mask], labels[:, i][mask])
                        all_preds[name].extend(outputs[name][mask].argmax(1).cpu().numpy())
                        all_true[name].extend(labels[:, i][mask].cpu().numpy())
                total_val_loss += loss.item()
        
        avg_train_loss = total_train_loss / len(train_loader)
        avg_val_loss = total_val_loss / len(val_loader)
        writer.add_scalar('Loss/Train', avg_train_loss, epoch)
        writer.add_scalar('Loss/Validation', avg_val_loss, epoch)
        
        print(f"\nEpoch {epoch+1}: Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f}")
        for name in target_cols:
            if all_true[name]:
                acc = accuracy_score(all_true[name], all_preds[name])
                writer.add_scalar(f"Accuracy/Val_{name}", acc, epoch)
                print(f"  -> Val Accuracy {name}: {acc:.4f}")

        scheduler.step(avg_val_loss)
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            torch.save(model.state_dict(), os.path.join(config['save_dir'], config['model_name']))
            print("  Model improved and saved.")
            
    writer.close()
    return model

# --- 5. Main Execution ---
if __name__ == "__main__":
    # --- MODIFIED: Load maps from files if in eval_only mode ---
    if CONFIG['eval_only']:
        print("--- Running in Evaluation-Only Mode ---")
        # Check if model and map files exist
        model_path = os.path.join(CONFIG['save_dir'], CONFIG['model_name'])
        label_map_path = os.path.join(CONFIG['save_dir'], CONFIG['label_maps_name'])
        missing_indices_path = os.path.join(CONFIG['save_dir'], CONFIG['missing_indices_name'])
        
        if not all(os.path.exists(p) for p in [model_path, label_map_path, missing_indices_path]):
            print("Error: Model file or map files not found. Cannot run in eval-only mode.")
            print("Please run a full training first by setting 'eval_only' to False.")
            exit()
            
        with open(label_map_path, 'r') as f:
            label_maps_str_keys = json.load(f)
            # Convert JSON string keys back to integers, as JSON saves all keys as strings
            label_maps = {task: {int(k): v for k, v in labels.items()} for task, labels in label_maps_str_keys.items()}
        with open(missing_indices_path, 'r') as f:
            missing_indices = json.load(f)
        
        # We still need to run prepare_data to get the splits, but we ignore the maps it produces
        _, val_df, target_cols, _, _ = prepare_data(CONFIG)
        train_df = pd.DataFrame() # No need for train_df in eval mode
    else:
        print("--- Running in Training & Evaluation Mode ---")
        train_df, val_df, target_cols, label_maps, missing_indices = prepare_data(CONFIG)
        # Create symlinks only during a full training run
        create_symlinks(train_df, 'train', CONFIG['symlink_dir'])
        create_symlinks(val_df, 'val', CONFIG['symlink_dir'])

    
    # Transforms are always needed
    val_transform = transforms.Compose([
        transforms.Resize((CONFIG['img_size'], CONFIG['img_size'])),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # DataLoaders are always needed
    if not CONFIG['eval_only']:
        train_transform = transforms.Compose([
            transforms.Resize((CONFIG['img_size'], CONFIG['img_size'])),
            transforms.RandomHorizontalFlip(),
            transforms.RandomRotation(15),
            transforms.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.3),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        train_dataset = UltrasoundCNNDataset(train_df, target_cols, train_transform)
        train_loader = DataLoader(train_dataset, batch_size=CONFIG['batch_size'], shuffle=True, num_workers=4, pin_memory=True)
    
    val_dataset = UltrasoundCNNDataset(val_df, target_cols, val_transform)
    val_loader = DataLoader(val_dataset, batch_size=CONFIG['batch_size'], shuffle=False, num_workers=4, pin_memory=True)
    
    # Model initialization is always needed to have the architecture
    model = UltrasoundCNNModel(label_maps)
    
    # --- MODIFIED: Conditionally run training ---
    if not CONFIG['eval_only']:
        model = run_training_and_validation(model, train_loader, val_loader, CONFIG, target_cols, missing_indices)
    
    # --- Final Evaluation on Best Model ---
    print("\n--- Final Evaluation on Best Model ---")
    device = torch.device("cuda" if torch.cuda.is_available() else "mps" if torch.backends.mps.is_available() else "cpu")
    model.load_state_dict(torch.load(os.path.join(CONFIG['save_dir'], CONFIG['model_name'])))
    model.to(device)
    model.eval()
    
    final_preds = {name: [] for name in target_cols}
    final_true = {name: [] for name in target_cols}
    with torch.no_grad():
        for inputs, labels in tqdm(val_loader, desc="Final Evaluation"):
            inputs, labels = inputs.to(device), labels.to(device)
            outputs = model(inputs)
            for i, name in enumerate(target_cols):
                mask = labels[:, i] != missing_indices[name]
                if mask.any():
                    final_preds[name].extend(outputs[name][mask].argmax(1).cpu().numpy())
                    final_true[name].extend(labels[:, i][mask].cpu().numpy())

    print("\n--- Final Reports and Confusion Matrices ---")
    for name in target_cols:
        if final_true[name]:
            unique_labels = np.unique(np.concatenate((final_true[name], final_preds[name]))).astype(int)
            
            # --- MODIFIED: Create bilingual labels using FEATURE_META_BILINGUAL ---
            bilingual_map = FEATURE_META_BILINGUAL.get(name, {})
            report_class_names = []
            for i in sorted(unique_labels):
                original_label = label_maps[name].get(i, str(i)) # Safely get the original label
                bilingual_label = bilingual_map.get(original_label, original_label)
                report_class_names.append(bilingual_label)

            print(f"\n--- Report for: {name} ---")
            print(classification_report(final_true[name], final_preds[name], target_names=report_class_names, labels=sorted(unique_labels), zero_division=0))
            
            cm = confusion_matrix(final_true[name], final_preds[name], labels=sorted(unique_labels))
            plot_confusion_matrix_func(cm, report_class_names, name, CONFIG['save_dir'])

    print("\nTraining and evaluation complete.") 