"""
多头模型验证和评估脚本
====================

主要功能:
- 加载训练好的多头分类模型进行验证
- 在验证集上计算详细的性能指标
- 生成分类报告和混淆矩阵
- 支持条件性评估（根据切面类型）

核心功能:
- 模型加载和设备配置
- 数据预处理和DataLoader创建
- 多任务条件性损失计算
- 各任务独立性能评估
- 可视化结果保存

验证指标:
- 准确率 (Accuracy)
- 精确率 (Precision)  
- 召回率 (Recall)
- F1分数 (F1-Score)
- 混淆矩阵 (Confusion Matrix)

条件逻辑:
- 纵向位置：仅在纵切图像上评估
- 横向位置：仅在横切图像上评估
- 叶位和峡部：仅在横切图像上评估
- 切面方向：所有图像都评估

输入数据:
- 训练好的模型文件 (.pth)
- 验证数据集 (CSV文件)
- 标签映射文件 (.json)

输出结果:
- 控制台性能报告
- 混淆矩阵图像文件
- 各任务的详细分类报告

作者: 项目团队
日期: 2024
"""

import os
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from torchvision import models, transforms
from PIL import Image
import json
from tqdm import tqdm
import argparse
import sys

# --- 1. Model Definition (Copied from train_position_classifier.py) ---
# This needs to be defined in the script to load the model state_dict
class ThyroidPositionModel(nn.Module):
    def __init__(self, label_maps):
        super(ThyroidPositionModel, self).__init__()
        
        self.num_lobe_classes = len(label_maps['lobe'])
        self.num_longitudinal_classes = len(label_maps['longitudinal'])
        self.num_transverse_classes = len(label_maps['transverse'])
        self.num_isthmus_classes = len(label_maps['isthmus'])
        self.num_orientation_classes = len(label_maps['orientation'])
        
        self.base_model = models.resnet50(weights=models.ResNet50_Weights.DEFAULT)
        num_features = self.base_model.fc.in_features
        self.base_model.fc = nn.Identity()
        
        self.lobe_classifier = nn.Linear(num_features, self.num_lobe_classes)
        self.longitudinal_classifier = nn.Linear(num_features, self.num_longitudinal_classes)
        self.transverse_classifier = nn.Linear(num_features, self.num_transverse_classes)
        self.isthmus_classifier = nn.Linear(num_features, self.num_isthmus_classes)
        self.orientation_classifier = nn.Linear(num_features, self.num_orientation_classes)
        
        self.dropout = nn.Dropout(p=0.2)
        
    def forward(self, x):
        features = self.base_model(x)
        features = self.dropout(features)
        
        lobe_logits = self.lobe_classifier(features)
        longitudinal_logits = self.longitudinal_classifier(features)
        transverse_logits = self.transverse_classifier(features)
        isthmus_logits = self.isthmus_classifier(features)
        orientation_logits = self.orientation_classifier(features)
        
        return lobe_logits, longitudinal_logits, transverse_logits, isthmus_logits, orientation_logits

# --- 2. Custom Dataset for Inference ---
class InferenceDataset(Dataset):
    def __init__(self, dataframe, transform=None):
        self.dataframe = dataframe
        self.transform = transform
        
    def __len__(self):
        return len(self.dataframe)
    
    def __getitem__(self, idx):
        row = self.dataframe.iloc[idx]
        img_path = row['image_path']
        sop_uid = row['sop_uid']
        
        try:
            image = Image.open(img_path).convert('RGB')
            if self.transform:
                image = self.transform(image)
        except Exception as e:
            # print(f"Warning: Could not load image {img_path}: {e}. Using a black image.", file=sys.stderr)
            image = torch.zeros((3, 224, 224))
        
        return image, sop_uid

# --- 3. Main Prediction Function ---
def run_inference(config):
    """
    Main function to run the model inference, process results, and save them.
    """
    output_file = config['output_file']
    # if os.path.exists(output_file):
    #     print(f"Output file '{output_file}' already exists. Skipping inference.")
    #     return

    # --- Device Selection ---
    if torch.cuda.is_available():
        device = torch.device("cuda")
    elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        device = torch.device("mps")
    else:
        device = torch.device("cpu")
    print(f"Using device: {device}")

    # --- Load Label Maps and Create Reverse Mappings ---
    try:
        with open(config['label_map_file'], 'r', encoding='utf-8') as f:
            label_maps = json.load(f)
    except FileNotFoundError:
        print(f"Error: Label map file '{config['label_map_file']}' not found.", file=sys.stderr)
        sys.exit(1)

    reverse_maps = {
        category: {int(idx): label for idx, label in mappings.items()}
        for category, mappings in label_maps.items()
    }

    # --- Load Model ---
    model = ThyroidPositionModel(label_maps)
    try:
        model.load_state_dict(torch.load(config['model_path'], map_location=device))
        model.to(device)
        model.eval()
        print(f"Model loaded successfully from {config['model_path']}")
    except Exception as e:
        print(f"Error loading model state_dict: {e}", file=sys.stderr)
        print("This might happen if the model was saved as a checkpoint. Trying to load from checkpoint structure.", file=sys.stderr)
        try:
            checkpoint = torch.load(config['model_path'], map_location=device)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.to(device)
            model.eval()
            print("Model loaded successfully from checkpoint.")
        except Exception as e2:
            print(f"Failed to load model from checkpoint as well: {e2}", file=sys.stderr)
            sys.exit(1)
            

    # --- Load and Prepare Data ---
    print(f"Loading data from '{config['input_excel']}'...")
    try:
        if config['input_excel'].endswith('.csv'):
            df = pd.read_csv(config['input_excel'], low_memory=False)
        else:
            df = pd.read_excel(config['input_excel'])
    except FileNotFoundError:
        print(f"Error: Input file '{config['input_excel']}' not found.", file=sys.stderr)
        sys.exit(1)
    
    # Construct the full image path
    # Assuming 'access_no' column exists in the excel. The user mentioned "access_np", which is likely a typo.
    try:
        df['image_path'] = df.apply(
            lambda row: os.path.join(config['image_base_dir'], str(row['access_no']), str(row['sop_uid']) + '.jpg'),
            axis=1
        )
    except KeyError:
        print("Error: 'access_no' or 'sop_uid' column not found in the Excel file.", file=sys.stderr)
        print("Please ensure the input excel has 'access_no' and 'sop_uid' columns.", file=sys.stderr)
        sys.exit(1)

    print(f"Total samples to process: {len(df)}")

    # --- Dataset and DataLoader ---
    data_transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    dataset = InferenceDataset(df, transform=data_transform)
    # Set num_workers=0 on macOS with MPS to avoid issues.
    num_workers = 0 if device.type == 'mps' else 4
    data_loader = DataLoader(dataset, batch_size=config['batch_size'], shuffle=False, num_workers=num_workers)

    # --- Run Inference ---
    results = []
    with torch.no_grad():
        for images, sop_uids in tqdm(data_loader, desc="Running Model Inference"):
            images = images.to(device)
            
            outputs = model(images)
            lobe_logits, long_logits, trans_logits, isth_logits, orient_logits = outputs
            
            # Get probabilities
            lobe_probs = torch.softmax(lobe_logits, dim=1)
            long_probs = torch.softmax(long_logits, dim=1)
            trans_probs = torch.softmax(trans_logits, dim=1)
            isth_probs = torch.softmax(isth_logits, dim=1)
            orient_probs = torch.softmax(orient_logits, dim=1)

            # Process batch results
            for i in range(len(sop_uids)):
                res = {'sop_uid': sop_uids[i]}
                
                # Process lobe (top 2)
                top2_lobe = torch.topk(lobe_probs[i], 2)
                res['lobe_pred_1'] = reverse_maps['lobe'][top2_lobe.indices[0].item()]
                res['lobe_conf_1'] = top2_lobe.values[0].item()
                if len(top2_lobe.indices) > 1:
                    res['lobe_pred_2'] = reverse_maps['lobe'][top2_lobe.indices[1].item()]
                    res['lobe_conf_2'] = top2_lobe.values[1].item()
                else:
                    res['lobe_pred_2'] = None
                    res['lobe_conf_2'] = 0.0

                # Process other heads (top 1)
                for name, probs, rev_map in [
                    ('orientation', orient_probs[i], reverse_maps['orientation']),
                    ('longitudinal', long_probs[i], reverse_maps['longitudinal']),
                    ('transverse', trans_probs[i], reverse_maps['transverse']),
                    ('isthmus', isth_probs[i], reverse_maps['isthmus']),
                ]:
                    top1_val, top1_idx = torch.max(probs, 0)
                    res[f'{name}_pred'] = rev_map[top1_idx.item()]
                    res[f'{name}_conf'] = top1_val.item()

                results.append(res)

    # --- Save Results ---
    print("Inference complete. Saving results...")
    results_df = pd.DataFrame(results)
    
    # Merge with original dataframe to keep all info
    final_df = pd.merge(df, results_df, on='sop_uid', how='left')
    
    # Save to new file, deciding format based on extension
    if output_file.endswith('.csv'):
        final_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    else:
        final_df.to_excel(output_file, index=False)
    print(f"Results saved to '{output_file}'")


# --- 4. Script Execution ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run multi-head thyroid position model for validation.")
    parser.add_argument(
        '--input_excel', 
        type=str, 
        default='sop_with_nodule_results_with_slice.xlsx',
        help='Path to the input Excel file.'
    )
    parser.add_argument(
        '--image_base_dir', 
        type=str, 
        default="/Users/<USER>/Documents/images/all_image_全图集合/all-20240809/images",
        help='Base directory where images are stored.'
    )
    parser.add_argument(
        '--model_path', 
        type=str, 
        default='models/thyroid_position_model_5_heads_full.pth',
        help='Path to the trained model file.'
    )
    parser.add_argument(
        '--label_map_file', 
        type=str, 
        default='label_maps_full.json',
        help='Path to the label map JSON file.'
    )
    parser.add_argument(
        '--output_file', 
        type=str, 
        default='sop_with_nodule_results_with_slice_model_v4.xlsx',
        help='Path to save the output Excel file with predictions.'
    )
    parser.add_argument(
        '--batch_size', 
        type=int, 
        default=32,
        help='Batch size for inference.'
    )
    
    args = parser.parse_args()
    
    config = vars(args)
    
    run_inference(config) 