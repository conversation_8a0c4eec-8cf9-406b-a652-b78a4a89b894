# Position Nodule Checkbox 功能实现文档

## 概述

在 `scripts/pair_viewer.py` 中成功添加了 **Position Nodule checkbox** 功能，实现了位置分类器和回归器的集成，支持配对结节的融合结果显示和颜色编码。

## 主要功能

### 1. 界面增强
- ✅ 在控制面板中添加了 "Position Nodule" checkbox
- ✅ 与现有的 "Detect Nodule" 和 "Classify Nodule" checkbox 保持一致的界面风格
- ✅ 根据模型可用性自动启用/禁用 checkbox

### 2. 模型集成
- ✅ 从 `config/config_pair.yaml` 读取模型路径：
  - `position_model_v1`: 位置分类器模型
  - `regressor_model_v1`: 3D坐标回归器模型
- ✅ 支持 MPS (Mac)、CUDA (GPU) 和 CPU 推理
- ✅ 优先使用硬件加速 (MPS > CUDA > CPU)

### 3. 位置预测功能
- ✅ **切面方向预测**: 使用 `position_model_v1` 预测纵切(0)或横切(1)
- ✅ **3D坐标预测**: 使用 `regressor_model` 预测 RL、UD、FB 坐标
- ✅ **位置字符串生成**: 将3D坐标映射为解剖位置字符串 (如 "RUF", "LLB" 等)

### 4. 配对融合逻辑
当两张图像构成有效配对时（不同的 orientation），执行以下融合策略：

```python
# 配对融合规则
RL_融合 = 横切图像的RL坐标  # 来自 orientation=1 的图像
UD_融合 = 纵切图像的UD坐标  # 来自 orientation=0 的图像  
FB_融合 = (图像1_FB + 图像2_FB) / 2  # 两张图像FB坐标的平均值
```

### 5. 数据更新机制
- ✅ 实时更新 position list 中的预测值
- ✅ 更新数据集中的 `pre_orientation`、`pre_RL`、`pre_UD`、`pre_FB` 字段
- ✅ 支持覆盖原有的批量推理结果，填补遗漏数据

### 6. 颜色编码显示
- ✅ **绿色**: 预测结果与GT一致
- ✅ **红色**: 预测结果与GT不一致  
- ✅ **黑色**: 默认颜色（无GT或无法比较）

## 技术实现细节

### 新增的核心方法

1. **`toggle_position()`**: 处理 checkbox 状态变化
2. **`run_position_pipeline()`**: 执行位置预测流水线
3. **`run_position_prediction()`**: 单张图像的位置预测
4. **`update_position_list_prediction()`**: 更新位置列表显示
5. **`update_dataset_position_values()`**: 更新数据集中的预测值
6. **`map_to_anatomical_position()`**: 3D坐标到位置字符串的映射

### 新增的模型类

```python
class ThyroidPositionClassifier(nn.Module):
    """甲状腺位置分类器 - 预测切面方向"""
    
class ThyroidPositionRegressor(nn.Module):
    """甲状腺位置回归器 - 预测3D坐标"""
```

### 配置文件更新

在 `config/config_pair.yaml` 中添加了模型路径配置：

```yaml
position_model_v1: models/thyroid_position_model_crop_v1.pth
regressor_model_v1: models_reg/thyroid_position_regressor_v1.pth
```

## 使用方法

### 基本操作
1. 启动 `scripts/pair_viewer.py`
2. 等待数据和模型加载完成
3. 勾选 "Position Nodule" checkbox
4. 系统自动对当前显示的图像进行位置预测
5. 查看 Position 表格中的预测结果和颜色编码

### 配对融合查看
1. 确保当前显示的是有效配对（不同 orientation）
2. 勾选 "Position Nodule" checkbox
3. 在 Position 表格的 "Pair" 列查看融合结果
4. 根据颜色判断预测准确性

## 数据集兼容性

- ✅ 兼容 `nodules_50k_bom_mix_dataset_v16_pair_v3.xlsx`
- ✅ 支持 27,811 个有效配对组
- ✅ 总计 55,622 张图像
- ✅ 100% 配对率（无单张图像）

## 性能特点

- ✅ **硬件加速**: 优先使用 MPS/GPU 进行推理
- ✅ **实时预测**: 切换图像时自动运行预测
- ✅ **内存优化**: 模型按需加载到设备
- ✅ **错误处理**: 完善的异常处理机制

## 调用方式参考

实现参考了 `scripts/batch_inference.py` 中的调用方式：

```python
# 设备选择
device = torch.device('mps' if torch.backends.mps.is_available() 
                     else 'cuda' if torch.cuda.is_available() 
                     else 'cpu')

# 模型推理
with torch.no_grad():
    orientation_logits = position_model(input_tensor)
    coordinates = regressor_model(input_tensor)
```

## 测试验证

- ✅ 通过 `scripts/test_position_checkbox.py` 验证功能完整性
- ✅ 验证配对融合逻辑正确性
- ✅ 验证颜色编码机制
- ✅ 验证数据更新机制

## 未来扩展

1. **模型优化**: 支持更多的位置分类模型
2. **融合策略**: 支持可配置的融合权重
3. **批量处理**: 支持批量位置预测
4. **结果导出**: 支持预测结果的导出功能

---

**实现状态**: ✅ 完成  
**测试状态**: ✅ 通过  
**文档状态**: ✅ 完整  

该功能已完全集成到 `pair_viewer.py` 中，可以立即使用。
