"""
甲状腺结节配对图像查看器
=======================

主要功能:
- 可视化结节的纵切和横切配对图像
- 集成YOLO检测和CNN分类功能
- 支持结节特征预测和位置分析
- 配对结节的3D坐标融合计算
- 实时显示模型预测结果和置信度

核心数据结构:
- PairViewer: 主窗口类，继承自tk.Tk
- UltrasoundCNNModel: CNN分类模型类
- YOLO: 目标检测模型（如果可用）
- cnn_target_cols: CNN分类的目标特征列表

界面布局:
- 左侧：双图像显示区域（上下排列）
- 右侧：控制面板，包含多个功能区域
  - 图像根目录选择
  - 当前图像信息显示
  - 位置预测结果表格
  - 结节特征分类结果表格
  - 详细信息文本区域

功能特性:
- 背景线程数据加载，提升用户体验
- 可切换不同的图像根目录
- 支持SOP UID和Access No复制
- 颜色编码的预测准确性指示
- 配对结节的坐标融合逻辑

配对逻辑:
- 同一access_no和nindex的两张图像
- 要求一张纵切(longitudinal)和一张横切(transverse)
- RL坐标来自横切，UD坐标来自纵切，FB取平均值

作者: 项目团队
日期: 2024
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import yaml
import pandas as pd
from PIL import Image, ImageTk, ImageDraw
import os
import torch
import torch.nn as nn
import cv2
import numpy as np
import torchvision.transforms as transforms
from torchvision import models
import json
import threading
try:
    from ultralytics import YOLO
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    ULTRALYTICS_AVAILABLE = False
    print("Warning: ultralytics not installed. YOLO detection will be disabled.")

# CNN Model Architecture (from training script)
class UltrasoundCNNModel(nn.Module):
    def __init__(self, label_maps):
        super(UltrasoundCNNModel, self).__init__()
        self.base_model = models.resnet50(weights=models.ResNet50_Weights.DEFAULT)
        num_features = self.base_model.fc.in_features
        self.base_model.fc = nn.Identity()
        
        self.dropout = nn.Dropout(0.5)
        
        self.classifiers = nn.ModuleDict({
            name: nn.Linear(num_features, len(classes)) for name, classes in label_maps.items()
        })
        
    def forward(self, x):
        features = self.base_model(x)
        features = self.dropout(features)
        outputs = {name: classifier(features) for name, classifier in self.classifiers.items()}
        return outputs

class PairViewer(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Ultrasound Image Pair Viewer")
        self.geometry(f"{self.winfo_screenwidth()}x{self.winfo_screenheight()}+0+0")

        self.config = {}
        self.image_df = None
        self.current_index = 0
        self.yolo_model = None
        self.cnn_model = None
        self.cnn_label_maps = None
        self.nodule_image1 = None
        self.nodule_image2 = None
        self.cnn_transforms = None
        self.cnn_target_cols = ['bom', 'ti_rads', 'std_composition', 'std_echo', 'std_ratio', 'std_margin', 'std_foci']
        self.loading_thread = None
        self.data_loaded = False

        self.main_frame = ttk.Frame(self)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        self.image_frame = ttk.Frame(self.main_frame)
        self.image_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        self.image1_label = ttk.Label(self.image_frame, text="正在加载数据...")
        self.image1_label.pack(side=tk.TOP, fill=tk.BOTH, expand=True)

        self.image2_label = ttk.Label(self.image_frame, text="请稍候...")
        self.image2_label.pack(side=tk.BOTTOM, fill=tk.BOTH, expand=True)

        # Set control panel to 50% of screen width
        control_panel_width = self.winfo_screenwidth() // 2
        self.control_panel = ttk.Frame(self.main_frame, width=control_panel_width)
        self.control_panel.pack(side=tk.RIGHT, fill=tk.Y)
        self.control_panel.pack_propagate(False)

        # Create loading progress bar
        self.progress_frame = ttk.Frame(self.control_panel)
        self.progress_frame.pack(pady=20, padx=10, fill=tk.X)
        
        self.progress_label = ttk.Label(self.progress_frame, text="正在加载配置和数据...")
        self.progress_label.pack(pady=5)
        
        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=5)
        self.progress_bar.start()

        self.create_control_widgets()
        
        # Disable controls initially
        self.set_controls_enabled(False)

        self.bind('<Left>', lambda event: self.prev_image())
        self.bind('<Right>', lambda event: self.next_image())
        self.bind('<Escape>', lambda event: self.quit())

        # Start background loading
        self.start_background_loading()

    def start_background_loading(self):
        """Start data loading in background thread"""
        self.loading_thread = threading.Thread(target=self.background_load_data, daemon=True)
        self.loading_thread.start()

    def background_load_data(self):
        """Load data in background thread"""
        try:
            # Update progress
            self.after(0, lambda: self.progress_label.config(text="正在加载配置文件..."))
            
            # Load config
            self._load_config_sync()
            
            # Update progress
            self.after(0, lambda: self.progress_label.config(text="正在加载模型..."))
            
            # Load models
            self.load_models()
            
            # Update progress
            self.after(0, lambda: self.progress_label.config(text="数据加载完成!"))
            
            # Mark as loaded and update UI
            self.data_loaded = True
            self.after(0, self.on_data_loaded)
            
        except Exception as e:
            error_msg = f"加载失败: {e}"
            self.after(0, lambda: self.show_error(error_msg))
            self.after(0, self.on_loading_failed)

    def _load_config_sync(self):
        """Synchronous config loading for background thread"""
        try:
            with open("config_pair.yaml", "r") as f:
                self.config = yaml.safe_load(f)
            
            label_file = self.config.get("label_file")
            if label_file and os.path.exists(label_file):
                # Update progress
                self.after(0, lambda: self.progress_label.config(text=f"正在读取数据文件: {os.path.basename(label_file)}..."))
                
                self.image_df = pd.read_excel(label_file)
                self.image_df = self.image_df.sort_values(by=['access_no', 'nindex']).reset_index(drop=True)
                self.current_index = 0
                
                # Update progress with data size
                data_size = len(self.image_df)
                self.after(0, lambda: self.progress_label.config(text=f"已加载 {data_size} 条记录"))
            else:
                print(f"Warning: Label file not found at {label_file}")
                self.image_df = None

        except Exception as e:
            print(f"Error loading config or data: {e}")
            self.image_df = None
            self.config = {}
            raise

    def on_data_loaded(self):
        """Called when data loading is complete"""
        # Hide progress bar
        self.progress_bar.stop()
        self.progress_frame.pack_forget()
        
        # Enable controls
        self.set_controls_enabled(True)
        self.update_widget_states()
        
        # Load first image if data is available
        if self.image_df is not None:
            self.load_image_pair()
        else:
            self.clear_displays()
            self.image1_label.config(text="未找到数据文件")
            self.image2_label.config(text="请检查配置")

    def on_loading_failed(self):
        """Called when data loading fails"""
        # Hide progress bar
        self.progress_bar.stop()
        self.progress_frame.pack_forget()
        
        # Show error message
        self.image1_label.config(text="数据加载失败")
        self.image2_label.config(text="请检查配置和文件")
        
        # Enable reload button only
        for widget in self.control_panel.winfo_children():
            if isinstance(widget, ttk.Button) and widget.cget('text') == 'Reload/Load':
                widget.config(state=tk.NORMAL)

    def set_controls_enabled(self, enabled):
        """Enable or disable all control widgets"""
        state = tk.NORMAL if enabled else tk.DISABLED
        for widget in self.control_panel.winfo_children():
            if isinstance(widget, (ttk.Button, ttk.Checkbutton)):
                widget.config(state=state)

    def create_control_widgets(self):
        # Image Root Selection Combobox (at top)
        root_frame = ttk.LabelFrame(self.control_panel, text="Image Root")
        root_frame.pack(pady=5, padx=5, fill=tk.X)
        
        self.image_root_var = tk.StringVar(value="image_base")
        self.image_root_combo = ttk.Combobox(root_frame, textvariable=self.image_root_var, 
                                            values=["image_base", "us_image_base", "nodule_image_base"],
                                            state="readonly")
        self.image_root_combo.pack(fill=tk.X, padx=5, pady=2)
        self.image_root_combo.bind('<<ComboboxSelected>>', self.on_image_root_changed)
        
        # SOP UID Display with context menu (at top)
        sop_frame = ttk.LabelFrame(self.control_panel, text="Current Image Info")
        sop_frame.pack(pady=5, padx=5, fill=tk.X)
        
        self.sop_uid_var = tk.StringVar()
        self.sop_uid_entry = ttk.Entry(sop_frame, textvariable=self.sop_uid_var, state="readonly")
        self.sop_uid_entry.pack(fill=tk.X, padx=5, pady=2)
        
        # Create context menu for sop_uid entry
        self.sop_context_menu = tk.Menu(self, tearoff=0)
        self.sop_context_menu.add_command(label="Copy SOP UID", command=self.copy_sop_uid)
        self.sop_context_menu.add_command(label="Copy Access No", command=self.copy_access_no)
        
        # Bind right-click to show context menu
        self.sop_uid_entry.bind("<Button-3>", self.show_sop_context_menu)  # Right-click on Windows/Linux
        self.sop_uid_entry.bind("<Button-2>", self.show_sop_context_menu)  # Right-click on macOS
        
        # Load/Reload button
        load_button = ttk.Button(self.control_panel, text="Reload/Load", command=self.reload_all_data)
        load_button.pack(pady=5, padx=5, fill=tk.X)

        # Prev/Next buttons
        btn_frame = ttk.Frame(self.control_panel)
        btn_frame.pack(pady=5, padx=5, fill=tk.X)
        prev_button = ttk.Button(btn_frame, text="Prev Image", command=self.prev_image)
        prev_button.pack(side=tk.LEFT, expand=True, fill=tk.X)
        next_button = ttk.Button(btn_frame, text="Next Image", command=self.next_image)
        next_button.pack(side=tk.RIGHT, expand=True, fill=tk.X)

        # Checkboxes
        self.detect_nodule_var = tk.BooleanVar()
        self.detect_check = ttk.Checkbutton(self.control_panel, text="Detect Nodule", variable=self.detect_nodule_var, command=self.toggle_detection)
        self.detect_check.pack(pady=5, padx=5, anchor='w')

        self.classify_nodule_var = tk.BooleanVar()
        self.classify_check = ttk.Checkbutton(self.control_panel, text="Classify Nodule", variable=self.classify_nodule_var, command=self.toggle_classification)
        self.classify_check.pack(pady=5, padx=5, anchor='w')
        
        self.update_widget_states()

        # Position List
        pos_frame = ttk.LabelFrame(self.control_panel, text="Position")
        pos_frame.pack(pady=10, padx=5, fill=tk.X, expand=False)
        self.position_tree = ttk.Treeview(pos_frame, columns=("Label", "GT", "Pred1", "Conf1", "Pred2", "Conf2", "Pair", "PairConf"), show="headings", height=5)
        self.position_tree.pack(fill=tk.BOTH, expand=True)
        self.position_tree.column("Label", width=80)
        for col in self.position_tree['columns'][1:]:
            self.position_tree.heading(col, text=col)
            self.position_tree.column(col, width=40, anchor='center')
        self.position_tree.heading("Label", text="Label")
        
        # Configure tags for color coding
        self.position_tree.tag_configure('correct', foreground='green')
        self.position_tree.tag_configure('incorrect', foreground='red')
        self.position_tree.tag_configure('default', foreground='black')

        # Nodule Feature List
        feature_frame = ttk.LabelFrame(self.control_panel, text="Nodule Features")
        feature_frame.pack(pady=10, padx=5, fill=tk.X, expand=False)
        self.feature_tree = ttk.Treeview(feature_frame, columns=("Label", "GT", "Pred1", "Conf1", "Pred2", "Conf2", "Pair", "PairConf"), show="headings", height=8)
        self.feature_tree.pack(fill=tk.BOTH, expand=True)
        self.feature_tree.column("Label", width=80)
        for col in self.feature_tree['columns'][1:]:
            self.feature_tree.heading(col, text=col)
            self.feature_tree.column(col, width=40, anchor='center')
        self.feature_tree.heading("Label", text="Label")
        
        # Configure tags for color coding
        self.feature_tree.tag_configure('correct', foreground='green')
        self.feature_tree.tag_configure('incorrect', foreground='red')
        self.feature_tree.tag_configure('default', foreground='black')

        # Textbox for description
        text_frame = ttk.LabelFrame(self.control_panel, text="Details")
        text_frame.pack(pady=10, padx=5, fill=tk.BOTH, expand=True)
        self.text_display = tk.Text(text_frame, wrap=tk.WORD, height=10)
        self.text_display.pack(fill=tk.BOTH, expand=True)
        self.text_display.tag_configure("bold", font=("TkDefaultFont", 10, "bold"))
        self.text_display.tag_config("red", foreground="red")
        self.text_display.tag_config("green", foreground="green")

    def on_image_root_changed(self, event=None):
        """Handle image root selection change"""
        if hasattr(self, 'current_index') and self.current_index >= 0 and self.data_loaded:
            self.load_image_pair()

    def show_sop_context_menu(self, event):
        """Show context menu for SOP UID entry"""
        try:
            self.sop_context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.sop_context_menu.grab_release()

    def copy_sop_uid(self):
        """Copy current SOP UID to clipboard"""
        if hasattr(self, 'image_info1') and self.image_info1 is not None:
            sop_uid = self.image_info1.get('sop_uid', '')
            if sop_uid:
                self.clipboard_clear()
                self.clipboard_append(str(sop_uid))
                print(f"Copied SOP UID: {sop_uid}")

    def copy_access_no(self):
        """Copy current access_no to clipboard"""
        if hasattr(self, 'image_info1') and self.image_info1 is not None:
            access_no = self.image_info1.get('access_no', '')
            if access_no:
                self.clipboard_clear()
                self.clipboard_append(str(access_no))
                print(f"Copied Access No: {access_no}")

    def reload_all_data(self):
        """Reload all data - can be called manually"""
        if self.loading_thread and self.loading_thread.is_alive():
            print("Already loading data, please wait...")
            return
            
        # Reset state
        self.data_loaded = False
        self.image_df = None
        
        # Show progress bar again
        self.progress_frame.pack(pady=20, padx=10, fill=tk.X)
        self.progress_bar.start()
        self.progress_label.config(text="正在重新加载数据...")
        
        # Disable controls
        self.set_controls_enabled(False)
        
        # Clear displays
        self.clear_displays()
        
        # Start background loading
        self.start_background_loading()

    def _load_config(self):
        """This method is now replaced by background loading"""
        pass  # Keep for compatibility, but functionality moved to background

    def update_widget_states(self):
        self.detect_check.config(state=tk.NORMAL if self.yolo_model else tk.DISABLED)
        self.classify_check.config(state=tk.NORMAL if self.cnn_model else tk.DISABLED)
        if not self.yolo_model:
            self.detect_nodule_var.set(False)
        if not self.cnn_model:
            self.classify_nodule_var.set(False)

    def load_config_and_data(self):
        try:
            with open("config_pair.yaml", "r") as f:
                self.config = yaml.safe_load(f)
            
            label_file = self.config.get("label_file")
            if label_file and os.path.exists(label_file):
                self.image_df = pd.read_excel(label_file)
                # Simple pair logic for now
                self.image_df = self.image_df.sort_values(by=['access_no', 'nindex']).reset_index(drop=True)
                self.current_index = 0
                self.load_image_pair()
            else:
                self.show_error("Label file not found.")

        except Exception as e:
            self.show_error(f"Error loading config or data: {e}")

    def load_models(self):
        self.yolo_model, self.cnn_model = None, None # Reset models
        # Load YOLO11
        if ULTRALYTICS_AVAILABLE:
            try:
                yolo_path = self.config.get('yolo_detect_model')
                if yolo_path and os.path.exists(yolo_path):
                    self.yolo_model = YOLO(yolo_path)
                    print(f"Successfully loaded YOLO11 model from {yolo_path}")
                else:
                    print("Warning: YOLO model path not found in config or file does not exist.")
            except Exception as e:
                print(f"Warning: Failed to load YOLO11 model: {e}. Detection will be disabled.")
                self.yolo_model = None
        else:
            print("Warning: ultralytics library not available. YOLO detection will be disabled.")

        # Load CNN
        try:
            cnn_path = self.config.get('cnn_classify_model')
            label_maps_path = os.path.join(os.path.dirname(cnn_path), 'cnn_features_label_maps_v16.json') if cnn_path else None
            
            if cnn_path and os.path.exists(cnn_path) and label_maps_path and os.path.exists(label_maps_path):
                # Load label maps first
                with open(label_maps_path, 'r') as f:
                    label_maps_str_keys = json.load(f)
                    # Convert JSON string keys back to integers
                    self.cnn_label_maps = {task: {int(k): v for k, v in labels.items()} for task, labels in label_maps_str_keys.items()}
                
                # Create model with correct architecture
                model = UltrasoundCNNModel(self.cnn_label_maps)
                
                # Load state dict
                state_dict = torch.load(cnn_path, map_location='cpu')
                model.load_state_dict(state_dict)
                model.eval()
                
                self.cnn_model = model
                self.cnn_transforms = transforms.Compose([
                    transforms.Resize((224, 224)),
                    transforms.ToTensor(),
                    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
                ])
                print(f"Successfully loaded CNN model from {cnn_path}")
                
            else:
                if not cnn_path or not os.path.exists(cnn_path):
                    print("Warning: CNN model path not found in config or file does not exist.")
                if not label_maps_path or not os.path.exists(label_maps_path):
                    print(f"Warning: CNN label maps file not found at {label_maps_path}")
                    
        except Exception as e:
            print(f"Warning: Failed to load CNN model: {e}. Classification will be disabled.")
            self.cnn_model = None
            self.cnn_label_maps = None

    def load_image_pair(self):
        if not self.data_loaded or self.image_df is None or self.image_df.empty:
            return

        self.clear_displays()

        self.image_info1 = self.image_df.iloc[self.current_index]
        self.image_info2 = None
        
        # Display image 1
        self.current_image1, self.original_image1 = self.get_image(self.image_info1)
        self.display_image(self.current_image1, self.image1_label)

        # Check for a pair
        if self.current_index + 1 < len(self.image_df):
            image_info2_candidate = self.image_df.iloc[self.current_index + 1]
            if self.image_info1['access_no'] == image_info2_candidate['access_no'] and self.image_info1['nindex'] == image_info2_candidate['nindex']:
                 self.image_info2 = image_info2_candidate
                 self.current_image2, self.original_image2 = self.get_image(self.image_info2)
                 self.display_image(self.current_image2, self.image2_label)
        
        self.update_info_panels()
        
        # Auto-run detection and classification based on checkbox states
        self.auto_run_pipelines()

    def get_image(self, image_info):
        # Ensure path components are strings to prevent errors with os.path.join
        access_no = str(image_info['access_no'])
        sop_uid = str(image_info['sop_uid'])
        
        # Get selected image root
        selected_root = self.image_root_var.get() if hasattr(self, 'image_root_var') else "image_base"
        if selected_root == "image_base":
            image_root = self.config.get('image_base', self.config.get('image_root', ''))
        elif selected_root == "us_image_base":
            image_root = self.config.get('us_image_base', self.config.get('image_root', ''))
        elif selected_root == "nodule_image_base":
            image_root = self.config.get('nodule_image_base', self.config.get('image_root', ''))
        else:
            image_root = self.config.get('image_base', self.config.get('image_root', ''))  # fallback
        
        image_path = os.path.join(image_root, access_no, f"{sop_uid}.jpg")
        if os.path.exists(image_path):
            img = Image.open(image_path).convert("RGB")
            return img, img.copy()
        return None, None

    def display_image(self, pil_image, image_label):
        if pil_image:
            # Resize image to fit the label
            label_width = self.image_frame.winfo_width()
            label_height = self.image_frame.winfo_height() / 2
            if label_width > 1 and label_height > 1:
                img_copy = pil_image.copy()
                img_copy.thumbnail((label_width, int(label_height)), Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(img_copy)
                image_label.config(image=photo)
                image_label.image = photo
        else:
            image_label.config(text=f"Image not found", image=None)

    def next_image(self):
        if not self.data_loaded or self.image_df is None or self.current_index >= len(self.image_df) - 1:
            return
            
        # Check if current is a pair
        if self.current_index + 1 < len(self.image_df):
            info1 = self.image_df.iloc[self.current_index]
            info2 = self.image_df.iloc[self.current_index+1]
            if info1['access_no'] == info2['access_no'] and info1['nindex'] == info2['nindex']:
                self.current_index += 2
            else:
                self.current_index += 1
        else:
            self.current_index += 1
        
        if self.current_index < len(self.image_df):
            self.load_image_pair()

    def prev_image(self):
        if not self.data_loaded or self.image_df is None or self.current_index <= 0:
            return
            
        # Check if previous was a pair
        if self.current_index - 2 >= 0:
            info1 = self.image_df.iloc[self.current_index-1]
            info2 = self.image_df.iloc[self.current_index-2]
            if info1['access_no'] == info2['access_no'] and info1['nindex'] == info2['nindex']:
                 self.current_index -= 2
            else:
                self.current_index -= 1
        else:
            self.current_index -= 1

        if self.current_index >= 0:
            self.load_image_pair()

    def auto_run_pipelines(self):
        """
        Automatically run detection and classification pipelines based on checkbox states
        when switching between images
        """
        if not self.data_loaded:
            return
            
        # Run detection if checkbox is checked
        if hasattr(self, 'detect_nodule_var') and self.detect_nodule_var.get():
            self.run_detection_pipeline()
        
        # Run classification if checkbox is checked
        if hasattr(self, 'classify_nodule_var') and self.classify_nodule_var.get():
            self.run_classification_pipeline()

    def run_detection_pipeline(self):
        """Run nodule detection on current images"""
        if not self.yolo_model:
            return
            
        self.nodule_image1 = None
        self.nodule_image2 = None

        # Process image 1
        if self.original_image1:
            self.current_image1, self.nodule_image1 = self.run_yolo_detection(self.original_image1)
            self.display_image(self.current_image1, self.image1_label)
        
        # Process image 2
        if hasattr(self, 'original_image2') and self.image_info2 is not None:
            self.current_image2, self.nodule_image2 = self.run_yolo_detection(self.original_image2)
            self.display_image(self.current_image2, self.image2_label)

    def run_classification_pipeline(self):
        """Run nodule classification on current nodule images"""
        if not self.cnn_model:
            return
            
        # Clear previous classification results
        self.update_feature_list_classification(None, 1)
        self.update_feature_list_classification(None, 2)

        preds1, preds2 = None, None
        if self.nodule_image1:
            preds1 = self.run_cnn(self.nodule_image1)
            self.update_feature_list_classification(preds1, 1)
        
        if self.nodule_image2:
            preds2 = self.run_cnn(self.nodule_image2)
            self.update_feature_list_classification(preds2, 2)
        
        self.calculate_pair_features(preds1, preds2)

    def run_pipelines(self):
        self.toggle_detection()
        self.toggle_classification()

    def toggle_detection(self):
        if self.detect_nodule_var.get() and self.yolo_model:
            self.run_detection_pipeline()
        else:
             # If unchecked, display original images
            if hasattr(self, 'original_image1'):
                self.current_image1 = self.original_image1.copy()
                self.display_image(self.current_image1, self.image1_label)
            if hasattr(self, 'original_image2') and self.image_info2 is not None:
                 self.current_image2 = self.original_image2.copy()
                 self.display_image(self.current_image2, self.image2_label)
        
        # After detection status changes, classification might need to be re-run
        if self.classify_nodule_var.get():
            self.run_classification_pipeline()

    def toggle_classification(self):
        if self.classify_nodule_var.get() and self.cnn_model:
            self.run_classification_pipeline()
        else:
            # Clear classification results when unchecked
            self.update_feature_list_classification(None, 1)
            self.update_feature_list_classification(None, 2)
            self.calculate_pair_features()

    def run_yolo_detection(self, image):
        results = self.yolo_model(image)
        nodule_crop = None

        # For YOLO11, the results structure is different
        if len(results) > 0 and len(results[0].boxes) > 0:
            boxes = results[0].boxes.xyxy.cpu().numpy()
            confs = results[0].boxes.conf.cpu().numpy()
            
            # Get the box with highest confidence
            best_idx = np.argmax(confs)
            x1, y1, x2, y2 = boxes[best_idx]

            # Calculate original box dimensions
            w, h = x2 - x1, y2 - y1
            center_x, center_y = x1 + w/2, y1 + h/2
            
            # Expand to 2W * 2H
            new_w, new_h = w * 2, h * 2
            
            # Calculate expanded box coordinates
            expand_x1 = max(0, center_x - new_w / 2)
            expand_y1 = max(0, center_y - new_h / 2)
            expand_x2 = min(image.width, center_x + new_w / 2)
            expand_y2 = min(image.height, center_y + new_h / 2)
            
            # Extract the expanded nodule image (2W*2H) BEFORE drawing any borders
            # This ensures the cropped image is not contaminated by drawn borders
            nodule_crop = image.crop((expand_x1, expand_y1, expand_x2, expand_y2))
            
            # Now draw green border for the expanded region (2W*2H) on the original image
            draw = ImageDraw.Draw(image)
            draw.rectangle([expand_x1, expand_y1, expand_x2, expand_y2], outline="green", width=3)

        return image, nodule_crop

    def run_cnn(self, nodule_image):
        if not nodule_image or not self.cnn_transforms or not self.cnn_model or not self.cnn_label_maps:
            return None
        
        try:
            # Preprocess image
            input_tensor = self.cnn_transforms(nodule_image).unsqueeze(0)
            
            # Run inference
            with torch.no_grad():
                outputs = self.cnn_model(input_tensor)
            
            # Process outputs
            preds = {}
            for task_name in self.cnn_target_cols:
                if task_name in outputs:
                    task_output = outputs[task_name].squeeze()
                    probabilities = torch.softmax(task_output, dim=0)
                    
                    # Get predicted class and confidence
                    pred_class_idx = torch.argmax(probabilities).item()
                    pred_confidence = probabilities[pred_class_idx].item()
                    
                    # Special handling for bom to ensure integer output
                    if task_name == 'bom':
                        # For bom, return integer class (0 or 1) directly
                        pred_label = int(pred_class_idx)
                        print(f"Debug: BOM prediction - class_idx={pred_class_idx}, pred_label={pred_label}, type={type(pred_label)}")
                    else:
                        # Map to original label if available for other tasks
                        if task_name in self.cnn_label_maps and pred_class_idx in self.cnn_label_maps[task_name]:
                            pred_label = self.cnn_label_maps[task_name][pred_class_idx]
                        else:
                            pred_label = pred_class_idx
                    
                    preds[task_name] = (pred_label, pred_confidence)

            return preds
            
        except Exception as e:
            print(f"CNN inference failed: {e}")
            return None

    def update_feature_list_classification(self, preds, image_num):
        if not preds: # Clear if no predictions
            for item_id in self.feature_tree.get_children():
                item = self.feature_tree.item(item_id)
                label = item['values'][0]
                if image_num == 1:
                    self.feature_tree.set(item_id, "Pred1", "")
                    self.feature_tree.set(item_id, "Conf1", "")
                else: # image_num == 2
                    self.feature_tree.set(item_id, "Pred2", "")
                    self.feature_tree.set(item_id, "Conf2", "")
            return

        for item_id in self.feature_tree.get_children():
            item = self.feature_tree.item(item_id)
            display_label = item['values'][0]  # This is the label shown in UI (e.g., 'composition')
            
            # Map display label back to CNN model label (e.g., 'composition' -> 'std_composition')
            if display_label in ['composition', 'echo', 'ratio', 'margin', 'foci']:
                cnn_label = f'std_{display_label}'
            else:
                cnn_label = display_label  # For 'bom' and 'ti_rads'
            
            if cnn_label in preds:
                pred_val, conf_val = preds[cnn_label]
                # Format to 2 decimal places if it's a float
                pred_str = f"{pred_val:.2f}" if isinstance(pred_val, float) else str(pred_val)
                conf_str = f"{conf_val:.2f}" if isinstance(conf_val, float) else str(conf_val)

                if image_num == 1:
                    self.feature_tree.set(item_id, "Pred1", pred_str)
                    self.feature_tree.set(item_id, "Conf1", conf_str)
                    
                    # Apply color coding for image 1 predictions
                    if display_label == 'bom':
                        gt_val = item['values'][1]  # GT value
                        if self.compare_bom_values(gt_val, pred_val):
                            self.feature_tree.item(item_id, tags=('correct',))
                        else:
                            self.feature_tree.item(item_id, tags=('incorrect',))
                else: # image_num == 2
                    self.feature_tree.set(item_id, "Pred2", pred_str)
                    self.feature_tree.set(item_id, "Conf2", conf_str)

    def calculate_pair_features(self, preds1=None, preds2=None):
        # Clear previous pair values
        for col in ["Pair", "PairConf"]:
            for item_id in self.position_tree.get_children():
                self.position_tree.set(item_id, col, "")
            for item_id in self.feature_tree.get_children():
                self.feature_tree.set(item_id, col, "")

        if self.image_info2 is None:
            return # Not a pair

        # Position Pairing Logic
        try:
            o1 = self.image_info1.get('pre_orientation')
            o2 = self.image_info2.get('pre_orientation')
            
            # Check for valid pair (0=longitudinal/纵切, 1=transverse/横切)
            if o1 is not None and o2 is not None and o1 != o2 and o1 in [0,1] and o2 in [0,1]:
                # Determine which is transverse (横切=1) and which is longitudinal (纵切=0)
                transverse_info = self.image_info1 if o1 == 1 else self.image_info2
                longitudinal_info = self.image_info1 if o1 == 0 else self.image_info2

                # Calculate pair values according to the specified logic
                pair_rl = transverse_info.get('pre_RL')  # RL from transverse image
                pair_ud = longitudinal_info.get('pre_UD')  # UD from longitudinal image
                
                # FB average from both images
                fb1 = transverse_info.get('pre_FB', 0)
                fb2 = longitudinal_info.get('pre_FB', 0)
                pair_fb = (fb1 + fb2) / 2 if fb1 is not None and fb2 is not None else None
                
                # Update tree with calculated values
                if pair_rl is not None:
                    self.set_pair_value('position', 'RL', f"{pair_rl:.3f}")
                if pair_ud is not None:
                    self.set_pair_value('position', 'UD', f"{pair_ud:.3f}")
                if pair_fb is not None:
                    self.set_pair_value('position', 'FB', f"{pair_fb:.3f}")

                # Calculate and map to anatomical position
                if pair_rl is not None and pair_ud is not None and pair_fb is not None:
                    pair_position = self.map_to_anatomical_position(pair_rl, pair_ud, pair_fb)
                    self.set_pair_value('position', 'position', pair_position)
                    
                    # Color code position based on RLUDFB distance with GT
                    self.update_position_colors_after_pairing(pair_rl, pair_ud, pair_fb)
                else:
                    self.set_pair_value('position', 'position', 'NaN')

            else: # Not a valid pair
                 for label in ['position', 'RL', 'UD', 'FB']:
                     self.set_pair_value('position', label, 'NaN')
        except Exception as e:
            print(f"Could not calculate pair position: {e}")
        
        # Feature Pairing Logic
        if preds1 and preds2:
            # Check if we have a valid image pair for bom special logic
            o1 = self.image_info1.get('pre_orientation')
            o2 = self.image_info2.get('pre_orientation')
            is_valid_pair = (o1 is not None and o2 is not None and o1 != o2 and o1 in [0,1] and o2 in [0,1])
            
            print(f"Debug: Feature pairing - o1={o1}, o2={o2}, is_valid_pair={is_valid_pair}")
            print(f"Debug: preds1 keys: {list(preds1.keys()) if preds1 else 'None'}")
            print(f"Debug: preds2 keys: {list(preds2.keys()) if preds2 else 'None'}")
            
            for cnn_label, (pred1, conf1) in preds1.items():
                if cnn_label in preds2:
                    pred2, conf2 = preds2[cnn_label]
                    
                    # Convert CNN label to display label for tree lookup
                    if cnn_label.startswith('std_'):
                        display_label = cnn_label.replace('std_', '')
                    else:
                        display_label = cnn_label
                    
                    print(f"Debug: Processing feature {cnn_label} -> {display_label}")
                    
                    # Special logic for bom feature
                    if display_label == 'bom':
                        print(f"Debug: BOM feature detected, is_valid_pair={is_valid_pair}")
                        if is_valid_pair:
                            print(f"Debug: Entering BOM pairing algorithm")
                            # Use the new weighted average algorithm for bom pairing
                            try:
                                # Try to convert predictions to numeric values
                                pred1_num = float(pred1) if pred1 is not None else 0
                                pred2_num = float(pred2) if pred2 is not None else 0
                                
                                # Convert predictions to binary if needed
                                pred1_binary = 1 if pred1_num > 0.5 else 0
                                pred2_binary = 1 if pred2_num > 0.5 else 0
                                
                                print(f"Debug: BOM pairing - pred1={pred1}({type(pred1)}) -> {pred1_binary}, pred2={pred2}({type(pred2)}) -> {pred2_binary}")
                                
                                # Calculate scores based on the new algorithm
                                # score1 = conf1 if(pred1==1) else (1-conf1)
                                score1 = conf1 if pred1_binary == 1 else (1 - conf1)
                                # score2 = conf2 if(pred2==1) else (1-conf2)  
                                score2 = conf2 if pred2_binary == 1 else (1 - conf2)
                                
                                # pair_score = (score1+score2)/2.0
                                pair_score = (score1 + score2) / 2.0
                                
                                # bom_pair = 1 if(pair_score>0.5) else 0
                                bom_pair = 1 if pair_score > 0.5 else 0
                                
                                # pair_conf = pair_score if(bom_pair==1) else (1-pair_score)
                                pair_conf = pair_score if bom_pair == 1 else (1 - pair_score)
                                
                                print(f"Debug: BOM pairing result - bom_pair={bom_pair}, pair_conf={pair_conf:.3f}")
                                print(f"Debug: About to call set_pair_value with display_label={display_label}")
                                
                                self.set_pair_value('feature', display_label, str(bom_pair), f"{pair_conf:.3f}")
                                
                                # Color code bom pair based on GT comparison
                                self.update_bom_pair_color(display_label, bom_pair, is_valid_pair)
                                
                            except (ValueError, TypeError) as e:
                                print(f"Debug: BOM pairing failed, using fallback - {e}")
                                # If conversion fails, concatenate
                                pair_pred = f"{pred1}/{pred2}"
                                pair_conf = (conf1 + conf2) / 2
                                self.set_pair_value('feature', display_label, pair_pred, f"{pair_conf:.2f}")
                        else:
                            print(f"Debug: BOM feature found but is_valid_pair=False, skipping pairing")
                        # If not valid pair, leave bom pair values empty (already cleared at start)
                    else:
                        # Regular pairing logic for other features
                        if isinstance(pred1, (int, float)) and isinstance(pred2, (int, float)):
                            pair_pred = (pred1 + pred2) / 2
                            pair_conf = (conf1 + conf2) / 2
                            self.set_pair_value('feature', display_label, f"{pair_pred:.2f}", f"{pair_conf:.2f}")
                        # For non-numeric, just concatenate or choose one
                        else:
                            pair_pred = f"{pred1}/{pred2}"
                            self.set_pair_value('feature', display_label, pair_pred)

    def update_position_colors_after_pairing(self, pair_rl, pair_ud, pair_fb):
        """Update position row color based on pair prediction vs GT"""
        try:
            # Get GT values
            position_gt1 = self.image_info1.get('position', 'N/A')
            rl_gt1, ud_gt1, fb_gt1 = self.parse_position_gt_to_rludfb(position_gt1)
            
            # Check consistency with pair prediction
            if self.is_position_consistent(rl_gt1, ud_gt1, fb_gt1, pair_rl, pair_ud, pair_fb):
                tag = 'correct'
            elif None not in [rl_gt1, ud_gt1, fb_gt1, pair_rl, pair_ud, pair_fb]:
                tag = 'incorrect'
            else:
                tag = 'default'
            
            # Update the position row color
            for item_id in self.position_tree.get_children():
                item = self.position_tree.item(item_id)
                if item['values'][0] == 'position':
                    self.position_tree.item(item_id, tags=(tag,))
                    break
        except Exception as e:
            print(f"Error updating position colors: {e}")

    def update_bom_pair_color(self, display_label, pair_pred, is_valid_pair):
        """Update bom row color based on pair prediction vs GT"""
        try:
            if is_valid_pair:
                # Get GT value for bom
                gt_bom = self.image_info1.get('bom', self.image_info1.get('bom', 'N/A'))
                
                # Compare pair prediction with GT
                if self.compare_bom_values(gt_bom, pair_pred):
                    tag = 'correct'
                else:
                    tag = 'incorrect'
                
                # Update the bom row color
                for item_id in self.feature_tree.get_children():
                    item = self.feature_tree.item(item_id)
                    if item['values'][0] == display_label:
                        self.feature_tree.item(item_id, tags=(tag,))
                        break
        except Exception as e:
            print(f"Error updating bom pair color: {e}")

    def map_to_anatomical_position(self, rl, ud, fb):
        """
        Map RL, UD, FB values to anatomical position description
        """
        try:
            # RL mapping (0~1)
            rl_part = ""
            if rl < 1/9:
                rl_part = "右叶外侧"
            elif rl < 2/9:
                rl_part = "右叶"
            elif rl < 3/9:
                if fb < 0.4:
                    rl_part = "右叶近峡部"
                else:
                    rl_part = "右叶内侧"
            elif rl < 4/9:
                rl_part = "峡部偏右"
            elif rl < 5/9:
                rl_part = "峡部"
            elif rl < 6/9:
                rl_part = "峡部偏左"
            elif rl < 7/9:
                if fb < 0.4:
                    rl_part = "左叶近峡部"
                else:
                    rl_part = "左叶内侧"
            elif rl < 8/9:
                rl_part = "左叶"
            else:  # rl >= 8/9
                rl_part = "左叶外侧"

            # UD mapping (0~1)
            ud_part = ""
            if ud < 1/7:
                ud_part = "上极"
            elif ud < 2/7:
                ud_part = "上部"
            elif ud < 3/7:
                ud_part = "中上部"
            elif ud < 4/7:
                ud_part = "中部"
            elif ud < 5/7:
                ud_part = "中下部"
            elif ud < 6/7:
                ud_part = "下部"
            else:  # ud >= 6/7
                ud_part = "下极"

            # FB mapping (0~1)
            fb_part = ""
            if fb < 0.125:
                fb_part = "前侧"
            elif fb < 0.375:
                fb_part = "近前侧"
            elif fb < 0.625:
                fb_part = "实质中央区"
            elif fb < 0.875:
                fb_part = "近背侧"
            else:  # fb >= 0.875
                fb_part = "背侧"

            # Combine parts: RL + UD + FB
            position_parts = [rl_part, ud_part, fb_part]
            
            # Remove "侧" from all but the last occurrence
            combined = ""
            for i, part in enumerate(position_parts):
                if "侧" in part and i < len(position_parts) - 1:
                    # Check if any subsequent part contains "侧"
                    has_later_side = any("侧" in p for p in position_parts[i+1:])
                    if has_later_side:
                        part = part.replace("侧", "")
                combined += part
            
            return combined
            
        except Exception as e:
            print(f"Error in position mapping: {e}")
            return "映射错误"

    def set_pair_value(self, tree_name, label, value, conf=''):
         tree = self.position_tree if tree_name == 'position' else self.feature_tree
         for item_id in tree.get_children():
            item = tree.item(item_id)
            if item['values'][0] == label:
                tree.set(item_id, 'Pair', value)
                tree.set(item_id, 'PairConf', conf)
                break

    def clear_displays(self):
        self.image1_label.config(image='', text="Image 1")
        self.image1_label.image = None
        self.image2_label.config(image='', text="Image 2")
        self.image2_label.image = None
        self.nodule_image1 = None
        self.nodule_image2 = None
        for i in self.position_tree.get_children():
            self.position_tree.delete(i)
        for i in self.feature_tree.get_children():
            self.feature_tree.delete(i)
        self.text_display.delete('1.0', tk.END)
        
    def show_error(self, message):
        messagebox.showerror("Error", message)

    def parse_position_gt_to_rludfb(self, position_text):
        """
        Parse position Ground Truth text and map to RL/UD/FB values
        """
        if not position_text or position_text == 'N/A' or pd.isna(position_text):
            return None, None, None
        
        position_text = str(position_text).strip()
        
        # Initialize values
        rl_val = None
        ud_val = None
        fb_val = None
        
        # FB mapping (前后, 0-1)
        if any(keyword in position_text for keyword in ['前侧', '前方被膜', '前被膜', '前包膜', '近表面']):
            fb_val = 0.0
        elif any(keyword in position_text for keyword in ['近前侧', '近前被膜']):
            fb_val = 0.25
        elif any(keyword in position_text for keyword in ['实质中央区', '未明确前后']):
            fb_val = 0.5
        elif any(keyword in position_text for keyword in ['近背侧', '近背膜处']):
            fb_val = 0.75
        elif any(keyword in position_text for keyword in ['背侧', '后侧', '后被膜']):
            fb_val = 1.0
        elif any(keyword in position_text for keyword in ['前', '背']) and '侧' not in position_text:
            # 仅出现前、背字（不带侧），隐含FB信息
            if '前' in position_text:
                fb_val = 0.0
            elif '背' in position_text:
                fb_val = 1.0
        
        # UD mapping (上下, 0-1) - 7个分段
        if '上极' in position_text:
            ud_val = 1/14  # N=1
        elif any(keyword in position_text for keyword in ['上部', '上段']):
            ud_val = 3/14  # N=2
        elif any(keyword in position_text for keyword in ['中上部', '上部及中部', '中上']):
            ud_val = 5/14  # N=3
        elif '中部' in position_text and '中上' not in position_text and '中下' not in position_text:
            ud_val = 7/14  # N=4
        elif any(keyword in position_text for keyword in ['中下部', '中下']):
            ud_val = 9/14  # N=5
        elif any(keyword in position_text for keyword in ['下部', '下段']):
            ud_val = 11/14  # N=6
        elif '下极' in position_text:
            ud_val = 13/14  # N=7
        # 横切面或未明确表述 → UD = NaN (保持None)
        
        # RL mapping (右左, 0-1) - 9等分
        # 右叶相关
        if '右叶' in position_text:
            if any(keyword in position_text for keyword in ['外侧', '近颈总动脉处']):
                rl_val = 0.5/9
            elif any(keyword in position_text for keyword in ['内侧', '近气管']):
                rl_val = 2.5/9
            elif '近峡部' in position_text:
                rl_val = 3.5/9  # 峡部右侧/右叶近峡部
            else:
                rl_val = 1.5/9  # 单纯右叶
        
        # 峡部相关
        elif '峡部' in position_text:
            if '右侧' in position_text or '右' in position_text:
                rl_val = 3.5/9
            elif '左侧' in position_text or '左' in position_text:
                rl_val = 5.5/9
            else:
                rl_val = 4.5/9  # 单纯峡部
        
        # 左叶相关
        elif '左叶' in position_text:
            if any(keyword in position_text for keyword in ['外侧', '近颈总动脉处']):
                rl_val = 8.5/9
            elif any(keyword in position_text for keyword in ['内侧', '近气管', '偏内侧']):
                rl_val = 6.5/9
            elif '近峡部' in position_text:
                rl_val = 5.5/9  # 峡部左侧/左叶近峡部
            else:
                rl_val = 7.5/9  # 单纯左叶
        
        # 处理单独出现的内、外字（不带侧）
        elif '内' in position_text and '侧' not in position_text:
            # 需要结合左右叶判断，这里假设是内侧的意思
            if '右' in position_text:
                rl_val = 2.5/9
            elif '左' in position_text:
                rl_val = 6.5/9
        elif '外' in position_text and '侧' not in position_text:
            # 需要结合左右叶判断，这里假设是外侧的意思
            if '右' in position_text:
                rl_val = 0.5/9
            elif '左' in position_text:
                rl_val = 8.5/9
        
        # 纵切面或未明确表述 → RL = NaN (保持None)
        
        return rl_val, ud_val, fb_val

    def calculate_rludfb_distance(self, rl1, ud1, fb1, rl2, ud2, fb2):
        """
        Calculate 3D Euclidean distance between two RLUDFB positions
        Skip NaN coordinates and only calculate distance for valid coordinates
        """
        try:
            # Collect valid coordinate pairs
            valid_distances = []
            
            # Check RL dimension
            if rl1 is not None and rl2 is not None and not (pd.isna(rl1) or pd.isna(rl2)):
                try:
                    rl1_val, rl2_val = float(rl1), float(rl2)
                    valid_distances.append((rl1_val - rl2_val) ** 2)
                except (ValueError, TypeError):
                    pass
            
            # Check UD dimension  
            if ud1 is not None and ud2 is not None and not (pd.isna(ud1) or pd.isna(ud2)):
                try:
                    ud1_val, ud2_val = float(ud1), float(ud2)
                    valid_distances.append((ud1_val - ud2_val) ** 2)
                except (ValueError, TypeError):
                    pass
            
            # Check FB dimension
            if fb1 is not None and fb2 is not None and not (pd.isna(fb1) or pd.isna(fb2)):
                try:
                    fb1_val, fb2_val = float(fb1), float(fb2)
                    valid_distances.append((fb1_val - fb2_val) ** 2)
                except (ValueError, TypeError):
                    pass
            
            # Calculate distance from valid dimensions
            if len(valid_distances) == 0:
                return float('inf')  # No valid coordinates to compare
            
            # Calculate Euclidean distance for valid dimensions
            distance = np.sqrt(sum(valid_distances))
            return distance
            
        except Exception as e:
            print(f"Error calculating distance: {e}")
            return float('inf')

    def is_position_consistent(self, gt_rl, gt_ud, gt_fb, pred_rl, pred_ud, pred_fb, threshold=0.3):
        """
        Check if position prediction is consistent with GT (distance < threshold)
        Only compare valid (non-NaN) coordinates
        """
        distance = self.calculate_rludfb_distance(gt_rl, gt_ud, gt_fb, pred_rl, pred_ud, pred_fb)
        
        # If no valid coordinates could be compared, return False (can't determine consistency)
        if distance == float('inf'):
            return False
            
        return distance < threshold

    def compare_bom_values(self, gt_bom, pred_bom):
        """
        Compare bom GT with prediction
        For binary classification: exact match required
        """
        try:
            if gt_bom is None or pred_bom is None or gt_bom == 'N/A' or pred_bom == 'N/A':
                return False
            
            # Convert to comparable format
            if isinstance(gt_bom, str):
                if gt_bom.replace('.', '').isdigit():
                    gt_val = int(float(gt_bom))  # Convert to int for binary comparison
                else:
                    return False
            else:
                gt_val = int(gt_bom)  # Convert to int for binary comparison
            
            if isinstance(pred_bom, str):
                if pred_bom.replace('.', '').isdigit():
                    pred_val = int(float(pred_bom))  # Convert to int for binary comparison
                else:
                    return False
            else:
                pred_val = int(pred_bom)  # Convert to int for binary comparison
            
            # For binary bom classification, exact match is required
            return gt_val == pred_val
            
        except (ValueError, TypeError):
            return False

    def update_info_panels(self):
        # Clear previous data
        for i in self.position_tree.get_children():
            self.position_tree.delete(i)
        for i in self.feature_tree.get_children():
            self.feature_tree.delete(i)

        image_info1 = self.image_info1
        image_info2 = self.image_info2
        
        # Update SOP UID display
        if hasattr(self, 'sop_uid_var') and image_info1 is not None:
            sop_uid = image_info1.get('sop_uid', 'N/A')
            access_no = image_info1.get('access_no', 'N/A')
            self.sop_uid_var.set(f"SOP: {sop_uid} | Access: {access_no}")

        # Get position GT and parse it to calculate RL/UD/FB GT values
        position_gt1 = image_info1.get('position', 'N/A')
        rl_gt1, ud_gt1, fb_gt1 = self.parse_position_gt_to_rludfb(position_gt1)
        
        position_gt2 = None
        rl_gt2, ud_gt2, fb_gt2 = None, None, None
        if image_info2 is not None:
            position_gt2 = image_info2.get('position', 'N/A')
            rl_gt2, ud_gt2, fb_gt2 = self.parse_position_gt_to_rludfb(position_gt2)

        # Check if we have a valid image pair
        o1 = image_info1.get('pre_orientation')
        o2 = image_info2.get('pre_orientation') if image_info2 is not None else None
        is_valid_pair = (o1 is not None and o2 is not None and o1 != o2 and o1 in [0,1] and o2 in [0,1])

        # Position Data
        pos_labels = ['position', 'orientation', 'RL', 'UD', 'FB']
        for label in pos_labels:
            # Determine color tag for this row
            tag = 'default'
            
            if label == 'position':
                gt1 = position_gt1
                gt2 = position_gt2 if position_gt2 is not None else ''
                
                # For position accuracy, check RLUDFB distance
                if not is_valid_pair:
                    # Compare GT with pred1 when no valid pair
                    pred1_rl = image_info1.get('pre_RL')
                    pred1_ud = image_info1.get('pre_UD') 
                    pred1_fb = image_info1.get('pre_FB')
                    if self.is_position_consistent(rl_gt1, ud_gt1, fb_gt1, pred1_rl, pred1_ud, pred1_fb):
                        tag = 'correct'
                    elif None not in [rl_gt1, ud_gt1, fb_gt1, pred1_rl, pred1_ud, pred1_fb]:
                        tag = 'incorrect'
                # For valid pairs, color will be updated after pair calculation
                        
            elif label == 'RL':
                gt1 = f"{rl_gt1:.3f}" if rl_gt1 is not None else 'NaN'
                gt2 = f"{rl_gt2:.3f}" if rl_gt2 is not None else ('NaN' if image_info2 is not None else '')
            elif label == 'UD':
                gt1 = f"{ud_gt1:.3f}" if ud_gt1 is not None else 'NaN'
                gt2 = f"{ud_gt2:.3f}" if ud_gt2 is not None else ('NaN' if image_info2 is not None else '')
            elif label == 'FB':
                gt1 = f"{fb_gt1:.3f}" if fb_gt1 is not None else 'NaN'
                gt2 = f"{fb_gt2:.3f}" if fb_gt2 is not None else ('NaN' if image_info2 is not None else '')
            else:  # orientation
                gt1 = image_info1.get(label, 'N/A')
                gt2 = image_info2.get(label, 'N/A') if image_info2 is not None else ''
            
            # Format orientation predictions with Chinese labels
            if label == 'orientation':
                pred1_raw = image_info1.get(f'pre_{label}', 'N/A')
                if pred1_raw == 0:
                    pred1 = "0(纵切)"
                elif pred1_raw == 1:
                    pred1 = "1(横切)"
                else:
                    pred1 = str(pred1_raw)
                
                pred2 = ''
                if image_info2 is not None:
                    pred2_raw = image_info2.get(f'pre_{label}', 'N/A')
                    if pred2_raw == 0:
                        pred2 = "0(纵切)"
                    elif pred2_raw == 1:
                        pred2 = "1(横切)"
                    else:
                        pred2 = str(pred2_raw)
            else:
                pred1 = image_info1.get(f'pre_{label}', 'N/A')
                pred2 = image_info2.get(f'pre_{label}', 'N/A') if image_info2 is not None else ''
            
            conf1 = image_info1.get(f'pre_{label}_conf', '')
            conf1 = f"{conf1:.2f}" if isinstance(conf1, float) else conf1

            conf2 = ''
            if image_info2 is not None:
                conf2 = image_info2.get(f'pre_{label}_conf', '')
                conf2 = f"{conf2:.2f}" if isinstance(conf2, float) else conf2

            # Insert with color tag
            item_id = self.position_tree.insert("", "end", values=(label, gt1, pred1, conf1, pred2, conf2, '', ''), tags=(tag,))

        # Feature Data - updated to match CNN model outputs
        feature_labels = ['bom', 'ti_rads', 'std_composition', 'std_echo', 'std_ratio', 'std_margin', 'std_foci']
        for label in feature_labels:
            # For display purposes, also check the non-std versions for ground truth
            display_label = label.replace('std_', '') if label.startswith('std_') else label
            gt1 = image_info1.get(label, image_info1.get(display_label, 'N/A'))
            
            gt2 = ''
            if image_info2 is not None:
                gt2 = image_info2.get(label, image_info2.get(display_label, 'N/A'))

            # Color coding will be applied after classification results are available
            self.feature_tree.insert("", "end", values=(display_label, gt1, '', '', gt2, '', '', ''), tags=('default',))

        # Text display
        self.text_display.delete('1.0', tk.END)
        self.text_display.insert(tk.END, "Description: ", "bold")
        self.text_display.insert(tk.END, f"{image_info1.get('description', 'N/A')}\n")
        self.text_display.insert(tk.END, "Impression: ", "bold")
        self.text_display.insert(tk.END, f"{image_info1.get('impression', 'N/A')}\n")
        self.text_display.insert(tk.END, "P Conclusion: ", "bold")

        p_conc = image_info1.get('p_conclusion', 'N/A')
        bom_tag = "red" if image_info1.get('bom', 0) == 1 else "green"
        self.text_display.insert(tk.END, f"{p_conc}\n", bom_tag)

if __name__ == "__main__":
    app = PairViewer()
    app.mainloop() 