"""
甲状腺结节标签可视化和验证工具
============================

主要功能:
- 交互式图像标签查看器，支持批量验证
- 实时模型预测和准确性验证
- 支持YOLO目标检测和3D坐标回归
- 双语标签显示和工具提示功能
- 结节配对分析和位置一致性检查

核心数据结构:
- LabelViewer: 主要的GUI应用类
- ThyroidPositionModel/ThyroidPositionRegressor: 加载的ML模型
- bilingual_map: 中英文标签映射
- Tooltip: 工具提示帮助类

界面组件:
- 图像显示区域：显示当前结节图像
- 标签树形视图：显示真实标签vs模型预测
- 导航按钮：上一张/下一张图像
- 模型选项：自动识别、气管检测、3D回归

输入数据:
- config.yaml: 配置文件（图像路径、模型路径等）
- CSV/Excel标签文件
- 训练好的模型文件 (.pth)
- 标签映射文件 (.json)

功能特性:
- 自动识别切换：在图像切换时自动运行预测
- 颜色编码：绿色=正确，红色=错误，黄色=部分匹配
- 右键菜单：复制预测结果到剪贴板
- 全量检查：批量处理所有图像

作者: 项目团队
日期: 2024
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk
import pandas as pd
import os
import torch
import torchvision.transforms as transforms
from train_position_classifier import ThyroidPositionModel
from train_position_regressor import ThyroidPositionRegressor
from remap_position_to_coords import parse_position_to_coordinates
import numpy as np
import yaml

# --- Tooltip Helper Class ---
class Tooltip:
    """
    Creates a tooltip for a given widget.
    """
    def __init__(self, widget):
        self.widget = widget
        self.tip_window = None

    def show(self, text, x, y):
        self.hide()  # Hide any existing tooltip
        if not text:
            return
        
        self.tip_window = tk.Toplevel(self.widget)
        self.tip_window.wm_overrideredirect(True)
        self.tip_window.wm_geometry(f"+{x}+{y}")
        
        label = tk.Label(self.tip_window, text=text, justify=tk.LEFT,
                         background="#ffffe0", relief=tk.SOLID, borderwidth=1,
                         font=("tahoma", "8", "normal"))
        label.pack(ipadx=1)

    def hide(self):
        if self.tip_window:
            self.tip_window.destroy()
        self.tip_window = None


class LabelViewer:
    def __init__(self, master):
        self.master = master
        self.master.title("Image Label Viewer")
        self.master.geometry("1400x800")

        # --- Config File ---
        self.config_file = "config.yaml"

        # --- Data Members ---
        self.img_root = tk.StringVar()
        self.label_file = tk.StringVar()
        self.model_file = tk.StringVar()
        self.label_map_file = tk.StringVar()
        self.model_regressor = tk.StringVar()
        self.yolo_model_path_var = tk.StringVar()

        self.dataframe = None
        self.image_paths = []
        self.current_index = -1
        self.label_widgets = []
        self.model = None
        self.loaded_model_path = None
        self.model_predictions = {}
        self.auto_recognize_var = tk.BooleanVar(value=False)
        self.detect_trachea_var = tk.BooleanVar(value=False)
        self.run_regressor_var = tk.BooleanVar(value=False)
        self.yolo_model = None
        self.regressor_model = None
        self.regressor_predictions = {}
        self.final_regressor_predictions = {}
        self.pairing_successful = False
        
        # --- Bilingual Label Mapping ---
        self.bilingual_map = {
            'image_path': 'Image Path (影像路径)',
            'position': 'Position (结节位置)',
            'orientation_label': 'Orientation (切面方向)',
            'lobe_label': 'Lobe (叶位)',
            'longitudinal_label': 'Longitudinal (纵向)',
            'transverse_label': 'Transverse (侧向)',
            'isthmus_label': 'Isthmus Relation (峡部关系)',
            'rl': '右左 (RL)',
            'ud': '上下 (UD)',
            'fb': '前后 (FB)',
            'reg_pos': '回归-位置',
        }

        # --- Model output label mappings ---
        self.orientation_labels = ["纵切", "横切"]
        self.lobe_labels = ['右叶', '峡部', '左叶']
        self.longitudinal_labels = ['无明确纵向位置', '上极', '上部', '中上部', '中部', '中下部', '下部', '下极']
        self.transverse_labels = ['无明确侧向位置', '前侧', '背侧', '外侧', '内侧']
        self.isthmus_labels = ['近峡部', '非近峡部']

        # --- Layout ---
        # Main frame that splits window into two columns
        self.main_frame = tk.Frame(master)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # Right control panel (created first to set its width)
        self.control_panel = tk.Frame(self.main_frame, width=525, relief=tk.RAISED, borderwidth=1)
        self.control_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=5)
        self.control_panel.pack_propagate(False) # Prevent panel from resizing to fit content

        # Left image display area
        self.image_label = tk.Label(self.main_frame, bg='black')
        self.image_label.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # --- Widgets for Control Panel ---
        self._create_control_widgets()

        # --- Bindings ---
        self._bind_keys()
        
        # --- Initial Load ---
        self._load_config() # Load config first
        self.master.after(100, self._initial_load)

    def _create_control_widgets(self):
        # Frame for controls. Path settings are now managed in config.yaml.
        controls_frame = tk.LabelFrame(self.control_panel, text="Controls", padx=10, pady=5)

        # Main action buttons
        action_frame = tk.Frame(controls_frame)
        action_frame.pack(fill=tk.X, pady=(0, 5), expand=True)
        tk.Button(action_frame, text="Load/Reload Data", command=self._load_data).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))
        tk.Button(action_frame, text="Check All", command=self._check_all_images).pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(2, 0))

        # Model options
        model_options_frame = tk.LabelFrame(controls_frame, text="Model Options", padx=10, pady=5)
        model_options_frame.pack(fill=tk.X, pady=5, expand=True)
        
        auto_rec_check = tk.Checkbutton(model_options_frame, text="自动识别 (切换图片时)", variable=self.auto_recognize_var, command=self._on_auto_recognize_toggle)
        auto_rec_check.pack(anchor='w')

        trachea_check = tk.Checkbutton(model_options_frame, text="检测气管", variable=self.detect_trachea_var, command=self._on_detect_trachea_toggle)
        trachea_check.pack(anchor='w')

        regressor_check = tk.Checkbutton(model_options_frame, text="3D回归模型", variable=self.run_regressor_var, command=self._on_run_regressor_toggle)
        regressor_check.pack(anchor='w')
        
        # Frame for navigation
        nav_frame = tk.LabelFrame(self.control_panel, text="Navigation", padx=10, pady=10)

        # Pack the main frames into the control panel
        nav_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
        controls_frame.pack(side=tk.TOP, fill=tk.X, padx=10, pady=10)
        self.labels_frame = tk.LabelFrame(self.control_panel, text="Labels", padx=10, pady=10)
        self.labels_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # --- Treeview for labels (goes inside self.labels_frame) ---
        columns = ('attribute', 'ground_truth', 'model_prediction', 'confidence')
        self.tree = ttk.Treeview(self.labels_frame, columns=columns, show='headings')

        self.tree.heading('attribute', text='属性')
        self.tree.heading('ground_truth', text='原始标签')
        self.tree.heading('model_prediction', text='模型预测')
        self.tree.heading('confidence', text='置信度')

        self.tree.column('attribute', width=120, anchor='w')
        self.tree.column('ground_truth', width=120, anchor='w')
        self.tree.column('model_prediction', width=120, anchor='w')
        self.tree.column('confidence', width=100, anchor='e')

        self.tree.tag_configure('green', background='#dff0d8')
        self.tree.tag_configure('yellow', background='#fcf8e3')
        self.tree.tag_configure('red', background='#f2dede')
        self.tree.tag_configure('blue', background='#d9edf7')
        self.tree.tag_configure('gray', foreground='gray')
        self.tree.tag_configure('green_text', foreground='green')

        self.tree.pack(fill=tk.BOTH, expand=True)

        self._create_tooltip_for_tree()
        self.tree.bind("<Button-3>", self._show_context_menu)

        # --- Navigation buttons (go inside nav_frame) ---
        prev_button = tk.Button(nav_frame, text="< Previous Image", command=self._prev_image)
        prev_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        next_button = tk.Button(nav_frame, text="Next Image >", command=self._next_image)
        next_button.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=5)

    def _bind_keys(self):
        self.master.bind('<Left>', lambda event: self._prev_image())
        self.master.bind('<Right>', lambda event: self._next_image())
        self.master.bind('<Escape>', lambda event: self.master.quit())

    def _initial_load(self):
        if os.path.exists(self.img_root.get()) and os.path.exists(self.label_file.get()):
            self._load_data()
        else:
            messagebox.showinfo("Welcome", "Please verify the default paths or select new ones, then click 'Load/Reload Data'.")

    def _load_config(self):
        try:
            with open(self.config_file, 'r') as f:
                config = yaml.safe_load(f)
            
            self.img_root.set(config.get('image_root', ''))
            self.label_file.set(config.get('label_file', ''))
            self.model_file.set(config.get('model_file', ''))
            self.label_map_file.set(config.get('label_map_file', ''))
            self.model_regressor.set(config.get('model_regressor', ''))
            self.yolo_model_path_var.set(config.get('yolo_model_path', ''))
            print("INFO: Configuration loaded from config.yaml")

        except FileNotFoundError:
            messagebox.showwarning("Config Not Found", f"Configuration file '{self.config_file}' not found. Please create it or check paths manually.")
        except Exception as e:
            messagebox.showerror("Config Error", f"Error loading configuration from '{self.config_file}':\n{e}")

    def _load_data(self):
        self._load_config() # Reload config on data load

        self.model = None  # Reset loaded model
        self.loaded_model_path = None

        img_root_path = self.img_root.get()
        label_file_path = self.label_file.get()
        label_map_path = self.label_map_file.get()

        if not all(os.path.exists(p) for p in [img_root_path, label_file_path, label_map_path]):
            messagebox.showerror("Error", "Please ensure all paths (Image Root, Label File, Label Map) are valid.")
            return
            
        try:
            if label_file_path.endswith('.csv'):
                # Add low_memory=False to suppress DtypeWarning for mixed-type columns
                self.dataframe = pd.read_csv(label_file_path, low_memory=False)
            else:
                self.dataframe = pd.read_excel(label_file_path)
            print(f"INFO: Loaded {len(self.dataframe)} records from {os.path.basename(label_file_path)}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load label file:\n{e}")
            return

        # --- FIX: Enhanced logic to find or create 'image_path' ---
        if 'image_path' not in self.dataframe.columns:
            print("INFO: 'image_path' column not found. Attempting to construct it from 'access_no' and 'sop_uid'.")
            required_cols = ['access_no', 'sop_uid']
            if all(col in self.dataframe.columns for col in required_cols):
                self.dataframe['image_path'] = self.dataframe.apply(
                    lambda row: os.path.join(str(row['access_no']), f"{row['sop_uid']}.jpg"), 
                    axis=1
                )
                print("INFO: 'image_path' column constructed successfully.")
            else:
                messagebox.showerror("Error", "Label file must contain either an 'image_path' column, or both 'access_no' and 'sop_uid' columns.")
                return
        
        # Now, 'image_path' is guaranteed to exist.
        self.image_paths = [os.path.join(img_root_path, p) for p in self.dataframe['image_path']]

        if not self.image_paths:
            messagebox.showwarning("Warning", "No image paths could be constructed from the label file.")
            return
        
        self.current_index = 0
        self._show_image()

    def _show_image(self):
        self.model_predictions = {}  # Clear old predictions
        self.regressor_predictions = {} # Clear old predictions
        self.final_regressor_predictions = {} # Clear combined predictions
        self.pairing_successful = False # Reset pairing status
        if not (0 <= self.current_index < len(self.image_paths)):
            self.image_label.config(image='', text="No image to display")
            return

        path = self.image_paths[self.current_index]
        if not os.path.exists(path):
            self.image_label.config(image='', text=f"Image not found:\n{path}")
            self._update_labels()
            return

        try:
            # Open image
            img = Image.open(path)

            # --- Run YOLO detection if the checkbox is enabled ---
            if self.detect_trachea_var.get():
                img = self._run_yolo_and_draw_box(img)

            # --- Resize image to fit the label widget ---
            widget_width = self.image_label.winfo_width()
            widget_height = self.image_label.winfo_height()
            
            if widget_width < 2 or widget_height < 2: # Widget not yet rendered
                widget_width, widget_height = 800, 800 # Fallback size

            img_aspect = img.width / img.height
            widget_aspect = widget_width / widget_height
            
            if img_aspect > widget_aspect: # Image is wider than widget
                new_width = widget_width
                new_height = int(new_width / img_aspect)
            else: # Image is taller than widget
                new_height = widget_height
                new_width = int(new_height * img_aspect)

            img_resized = img.resize((new_width, new_height), Image.LANCZOS)
            # --- End resizing ---

            photo = ImageTk.PhotoImage(img_resized)
            self.image_label.config(image=photo, text="")
            self.image_label.image = photo  # Keep a reference!
        except Exception as e:
            self.image_label.config(image='', text=f"Error loading image:\n{path}\n{e}")
        
        self._update_labels()

    def _update_labels(self):
        # Clear previous items in the tree
        for item in self.tree.get_children():
            self.tree.delete(item)

        if not (0 <= self.current_index < len(self.dataframe)):
            return

        data_row = self.dataframe.iloc[self.current_index]
        ground_truth_pos = data_row.get('position', 'N/A')
        
        # --- Calculate Ground Truth Coordinates ---
        gt_coords = parse_position_to_coordinates(ground_truth_pos)
        gt_rl, gt_ud, gt_fb = gt_coords

        # --- Model Prediction Data ---
        orientation = data_row.get('orientation_label')
        color_tag = self._get_correctness_tag(ground_truth_pos, self.model_predictions, orientation)
        predicted_pos_str = self._assemble_predicted_position(self.model_predictions)

        # 2. Now, loop through and display each row
        display_order = [
            'image_path', 'position', 'orientation_label', 'lobe_label',
            'longitudinal_label', 'transverse_label', 'isthmus_label'
        ]

        # Conditionally add the regressor comparison rows
        if self.run_regressor_var.get() and self.final_regressor_predictions:
            display_order.extend(['rl', 'ud', 'fb', 'reg_pos'])

        for key in display_order:
            bilingual_name = self.bilingual_map.get(key, key)
            
            truth_value, pred_value, conf_value, row_tag = '', '', '', ()

            if key == 'position':
                truth_value = ground_truth_pos
                pred_value = predicted_pos_str if self.model_predictions else ''
                conf_value = ''
                row_tag = (color_tag,) if color_tag else ()
            
            elif key == 'image_path':
                truth_value = data_row.get(key, 'N/A')

            elif key in ['rl', 'ud', 'fb']:
                # Ground truth comes from the parsed position string
                if key == 'rl': truth_value = gt_rl
                elif key == 'ud': truth_value = gt_ud
                elif key == 'fb': truth_value = gt_fb
                
                # Prediction comes from the (possibly paired) regressor results
                pred_value = self.final_regressor_predictions.get(key)
                
                # Gray out invalid rows based on current image orientation
                current_orientation = data_row.get('orientation_label')
                row_tag = ()
                if (key == 'ud' and current_orientation == 1) or \
                   (key == 'rl' and current_orientation == 0):
                    row_tag = ('gray',)

            elif key == 'reg_pos':
                 truth_value = '' # No ground truth for final combined position
                 pred_value = self._assemble_regressor_position(**self.final_regressor_predictions)
                 conf_value = ''
                 row_tag = ('green_text',) if self.pairing_successful else ()
            
            else: # Handle all individual classifier label rows
                # Dynamically generate ground truth from position string for consistency
                if key == 'orientation_label':
                    if pd.notna(data_row.get(key)):
                        truth_value = self.orientation_labels[int(data_row[key])]
                    else:
                        truth_value = "N/A"
                elif key == 'lobe_label':
                    truth_value = self._categorize_leaf(ground_truth_pos)
                elif key == 'longitudinal_label':
                    truth_value = self._categorize_vertical(ground_truth_pos)
                elif key == 'transverse_label':
                    truth_value = self._categorize_lateral(ground_truth_pos)
                elif key == 'isthmus_label':
                    truth_value = self._categorize_isthmus(ground_truth_pos)
                
                prediction_list = self.model_predictions.get(key, [])
                if prediction_list:
                    pred_value = prediction_list[0][0]
                    conf_value = prediction_list[0][1]

            # Format for display
            display_truth = f"{truth_value:.4f}" if isinstance(truth_value, (float, np.floating)) else str(truth_value) if pd.notna(truth_value) else 'N/A'
            display_pred = f"{pred_value:.4f}" if isinstance(pred_value, (float, np.floating)) else str(pred_value) if pd.notna(pred_value) else ''
            # Use robust type check for confidence
            display_conf = f"{conf_value:.2%}" if isinstance(conf_value, (float, np.floating)) else ''
            
            values = (bilingual_name, display_truth, display_pred, display_conf)
            self.tree.insert('', tk.END, values=values, tags=row_tag)

    def _next_image(self):
        if self.current_index < len(self.image_paths) - 1:
            self.current_index += 1
            self._show_image()
            if self.auto_recognize_var.get():
                self.master.after(10, self._auto_recognize)
            if self.run_regressor_var.get():
                self.master.after(20, self._run_regressor)

    def _prev_image(self):
        if self.current_index > 0:
            self.current_index -= 1
            self._show_image()
            if self.auto_recognize_var.get():
                self.master.after(10, self._auto_recognize)
            if self.run_regressor_var.get():
                self.master.after(20, self._run_regressor)

    def _on_auto_recognize_toggle(self):
        """Called when the auto-recognize checkbox is toggled."""
        if self.auto_recognize_var.get():
            # If the box is checked, run recognition on the current image immediately.
            self._auto_recognize()
        else:
            # If unchecked, clear the model predictions from the display.
            self.model_predictions = {}
            self._update_labels()

    def _auto_recognize(self):
        model_path = self.model_file.get()
        label_map_path = self.label_map_file.get()

        if not os.path.exists(model_path):
            messagebox.showerror("Error", f"Model file not found:\n{model_path}")
            return
        if not os.path.exists(label_map_path):
            messagebox.showerror("Error", f"Label map file not found:\n{label_map_path}")
            return

        if not (0 <= self.current_index < len(self.image_paths)):
            messagebox.showinfo("Info", "Please load an image first.")
            return

        current_image_path = self.image_paths[self.current_index]
        if not os.path.exists(current_image_path):
            messagebox.showerror("Error", f"Current image file not found:\n{current_image_path}")
            return

        try:
            # Load model only if it's not loaded or the path has changed
            if self.model is None or self.loaded_model_path != model_path:
                import json
                with open(label_map_path, 'r') as f:
                    label_maps = json.load(f)
                
                self.model = ThyroidPositionModel(label_maps)
                checkpoint = torch.load(model_path, map_location=torch.device('cpu'))
                if 'model_state_dict' in checkpoint:
                    self.model.load_state_dict(checkpoint['model_state_dict'])
                else:
                    self.model.load_state_dict(checkpoint)

                self.model.eval()
                self.loaded_model_path = model_path

            transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ])

            image = Image.open(current_image_path).convert('RGB')
            input_tensor = transform(image).unsqueeze(0)

            with torch.no_grad():
                outputs = self.model(input_tensor)
                lobe_logits, long_logits, trans_logits, isth_logits, orient_logits = outputs[0], outputs[1], outputs[2], outputs[3], outputs[4]

            self.model_predictions = {}
            
            # --- FIX: Store top-2 predictions instead of just top-1 ---
            output_map_for_prediction = {
                'orientation_label': (orient_logits, self.orientation_labels),
                'lobe_label': (lobe_logits, self.lobe_labels),
                'longitudinal_label': (long_logits, self.longitudinal_labels),
                'transverse_label': (trans_logits, self.transverse_labels),
                'isthmus_label': (isth_logits, self.isthmus_labels),
            }

            for key, (output_tensor, labels) in output_map_for_prediction.items():
                probabilities = torch.softmax(output_tensor, dim=1).squeeze()
                
                # Ensure we don't request more k than available classes
                k = min(2, len(labels))
                if k == 0: continue
                
                topk_probs, topk_indices = torch.topk(probabilities, k)

                topk_probs = topk_probs.cpu().numpy()
                topk_indices = topk_indices.cpu().numpy()

                top_results = []
                for i in range(k):
                    label_name = labels[topk_indices[i]]
                    confidence = topk_probs[i]
                    top_results.append((label_name, confidence))

                self.model_predictions[key] = top_results
            
            self._update_labels()

        except Exception as e:
            messagebox.showerror("Recognition Error", f"An error occurred during model inference:\n{e}")
            self.model = None  # Reset model on error
            self.loaded_model_path = None
            self.model_predictions = {}
            self._update_labels()

    def _create_tooltip_for_tree(self):
        self.tree_tooltip = Tooltip(self.master)
        self.tree.bind('<Motion>', self._on_tree_motion)
        self.tree.bind('<Leave>', self._on_tree_leave)
        # Create reverse map for easy lookup from display name to data key
        self.reverse_bilingual_map = {v: k for k, v in self.bilingual_map.items()}

    def _on_tree_motion(self, event):
        item_id = self.tree.identify_row(event.y)
        if not item_id:
            self.tree_tooltip.hide()
            return

        values = self.tree.item(item_id, 'values')
        if not values:
            self.tree_tooltip.hide()
            return
            
        bilingual_name = values[0]
        key = self.reverse_bilingual_map.get(bilingual_name)
        
        tooltip_text = self._format_tooltip_text(key)

        if tooltip_text:
            # Position the tooltip near the cursor
            self.tree_tooltip.show(tooltip_text, event.x_root + 15, event.y_root + 10)
        else:
            self.tree_tooltip.hide()
            
    def _on_tree_leave(self, event):
        self.tree_tooltip.hide()

    def _format_tooltip_text(self, key):
        if not self.model_predictions or key not in self.model_predictions:
            return ""
            
        top_results = self.model_predictions[key]
        
        if not top_results:
            return ""
            
        # If top-1 confidence is > 85%, show only top-1
        if top_results[0][1] > 0.85:
            return f"{top_results[0][0]}: {top_results[0][1]:.2%}"
            
        # Otherwise, show top-2 (if available)
        text = f"1. {top_results[0][0]}: {top_results[0][1]:.2%}"
        if len(top_results) > 1:
            text += f"\n2. {top_results[1][0]}: {top_results[1][1]:.2%}"
            
        return text

    def _show_context_menu(self, event):
        """Shows a context menu on right-click to copy IDs."""
        item_id = self.tree.identify_row(event.y)
        if not item_id:
            return

        # Ensure the clicked row is selected for visual feedback
        self.tree.selection_set(item_id)

        if not (0 <= self.current_index < len(self.dataframe)):
            return

        data_row = self.dataframe.iloc[self.current_index]
        access_no = data_row.get('access_no')
        sop_uid = data_row.get('sop_uid')

        # Only show the menu if there is something to copy
        if pd.isna(access_no) and pd.isna(sop_uid):
            return

        context_menu = tk.Menu(self.master, tearoff=0)
        
        if pd.notna(access_no):
            # Use str() to handle potential numeric types from pandas
            context_menu.add_command(label=f"Copy Access No: {access_no}", 
                                     command=lambda: self._copy_to_clipboard(str(access_no)))
        
        if pd.notna(sop_uid):
            context_menu.add_command(label=f"Copy SOP UID: {sop_uid}", 
                                     command=lambda: self._copy_to_clipboard(str(sop_uid)))
        
        context_menu.post(event.x_root, event.y_root)

    def _copy_to_clipboard(self, text):
        """Clears the clipboard and appends the given text."""
        self.master.clipboard_clear()
        self.master.clipboard_append(text)
        print(f"INFO: Copied to clipboard: '{text}'")

    def _assemble_predicted_position(self, model_preds_dict):
        """Assembles a position string from individual predicted labels."""
        # This logic should mirror how a final position string would be constructed.
        # It's a simplified example.
        
        # Get top-1 prediction text for each part
        lobe = model_preds_dict.get('lobe_label', [('', 0)])[0][0]
        longitudinal = model_preds_dict.get('longitudinal_label', [('', 0)])[0][0]
        transverse = model_preds_dict.get('transverse_label', [('', 0)])[0][0]
        isthmus = model_preds_dict.get('isthmus_label', [('', 0)])[0][0]
        
        parts = []
        if lobe: parts.append(lobe)
        # Avoid adding "default" or "unknown" values to the string
        if longitudinal and '无明确' not in longitudinal: parts.append(longitudinal)
        if transverse and '无明确' not in transverse: parts.append(transverse)
        if isthmus and '非近峡部' not in isthmus: parts.append(isthmus)
        
        return "".join(parts)

    def _categorize_leaf(self, position):
        if not isinstance(position, str): return '其他'
        if '左叶' in position: return '左叶'
        if '右叶' in position: return '右叶'
        if '峡部' in position: return '峡部'
        return '其他'

    def _categorize_vertical(self, position):
        if not isinstance(position, str): return '无明确纵向位置'
        if '中上部' in position: return '中上部'
        if '中下部' in position: return '中下部'
        if '上极' in position: return '上极'
        if '上部' in position: return '上部'
        if '下部' in position: return '下部'
        if '下极' in position: return '下极'
        if '中部' in position: return '中部'
        return '无明确纵向位置'

    def _categorize_lateral(self, position):
        if not isinstance(position, str): return '无明确侧向位置'
        if '前侧' in position: return '前侧'
        if '背侧' in position: return '背侧'
        if '外侧' in position: return '外侧'
        if '内侧' in position: return '内侧'
        return '无明确侧向位置'

    def _categorize_isthmus(self, position):
        if not isinstance(position, str): return '非近峡部'
        return '近峡部' if '近峡部' in position else '非近峡部'

    def _get_correctness_tag(self, ground_truth_pos, model_predictions, orientation_label):
        """
        Determines the correctness tag (green, yellow, blue, red) based on the final logic.
        """
        if not model_predictions:
            return ''

        predicted_pos_str = self._assemble_predicted_position(model_predictions)

        # 1. Green Condition: Perfect match
        if ground_truth_pos == predicted_pos_str:
            return 'green'

        # If not a perfect match, analyze further
        gt_lobe = self._categorize_leaf(ground_truth_pos)
        pred_lobe_data = model_predictions.get('lobe_label')
        if not pred_lobe_data:
            return 'red' # Cannot determine lobe, so it's a hard error
        pred_lobe = pred_lobe_data[0][0]

        # 2. Blue Condition: Longitudinal scan, only lobe is wrong
        if orientation_label == 0:  # 0 is '纵切'
            gt_pos_without_lobe = ground_truth_pos.replace(gt_lobe, "", 1) if gt_lobe != '其他' else ground_truth_pos
            pred_pos_without_lobe = predicted_pos_str.replace(pred_lobe, "", 1) if pred_lobe != '其他' else predicted_pos_str
            if gt_lobe != pred_lobe and gt_pos_without_lobe == pred_pos_without_lobe:
                return 'blue'

        # 3. Yellow Condition: Lobe matches, but other parts are wrong
        if gt_lobe == pred_lobe and gt_lobe != '其他':
            return 'yellow'

        # 4. Red Condition: All other errors
        return 'red'

    def _check_all_images(self):
        """Performs batch inference on all images in the loaded label file."""
        print("\n--- [Check All] Process Started ---")
        # 1. Validation
        model_path, label_map_path, label_file_path, img_root = self.model_file.get(), self.label_map_file.get(), self.label_file.get(), self.img_root.get()
        if not all(os.path.exists(p) for p in [model_path, label_map_path, label_file_path, img_root]):
            messagebox.showerror("Error", "Please ensure all paths (Model, Label Map, Label File, Image Root) are correct.")
            print("--- [Check All] Aborted: A required path was not found.")
            return

        # 2. Load Model
        try:
            if self.model is None or self.loaded_model_path != model_path:
                print("Loading model...")
                import json
                with open(label_map_path, 'r') as f: label_maps = json.load(f)
                self.model = ThyroidPositionModel(label_maps)
                checkpoint = torch.load(model_path, map_location=torch.device('cpu'))
                self.model.load_state_dict(checkpoint.get('model_state_dict', checkpoint))
                self.model.eval()
                self.loaded_model_path = model_path
                print("Model loaded successfully.")
        except Exception as e:
            messagebox.showerror("Model Load Error", f"Failed to load the model: {e}")
            print(f"--- [Check All] Aborted: Model load failed. {e}")
            return

        # 3. Setup Progress Bar and Data
        try:
            df = pd.read_csv(label_file_path) if label_file_path.endswith('.csv') else pd.read_excel(label_file_path)
            print(f"Label file loaded. Found {len(df)} records.")
        except Exception as e:
            messagebox.showerror("File Load Error", f"Failed to load label file: {e}")
            print(f"--- [Check All] Aborted: Label file load failed. {e}")
            return
        
        total_images = len(df)
        progress_window, progress_bar = self._create_progress_bar(total_images)
        print(f"Starting inference for {total_images} images...")

        # 4. Batch Inference Loop
        all_results, stats = [], {'green': 0, 'yellow': 0, 'blue': 0, 'red': 0}
        transform = transforms.Compose([transforms.Resize((224, 224)), transforms.ToTensor(), transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])])

        for index, row in df.iterrows():
            self.master.update_idletasks()
            progress_bar['value'] = index + 1
            
            full_path = os.path.join(img_root, row.get('image_path', ''))
            if not os.path.exists(full_path):
                print(f"[{index + 1}/{total_images}] SKIP - Image not found: {full_path}")
                continue

            try:
                image = Image.open(full_path).convert('RGB')
                input_tensor = transform(image).unsqueeze(0)
                with torch.no_grad(): outputs = self.model(input_tensor)
                
                predictions = self._parse_model_outputs(outputs)
                
                # --- FIX: Use the new centralized logic function for batch processing ---
                gt_pos = row.get('position', '')
                orientation = row.get('orientation_label')
                tag = self._get_correctness_tag(gt_pos, predictions, orientation)
                if not tag: tag = 'red' # Default to red if no prediction
                stats[tag] += 1
                
                predicted_pos_str = self._assemble_predicted_position(predictions)
                result_row = row.to_dict()
                result_row['predicted_position'] = predicted_pos_str
                result_row['correctness'] = tag
                for key, val in predictions.items():
                    result_row[f'pred_{key}'] = val[0][0]
                    result_row[f'conf_{key}'] = val[0][1]
                all_results.append(result_row)
                print(f"[{index + 1}/{total_images}] Pred: '{predicted_pos_str}' | GT: '{gt_pos}' | Result: {tag.upper()}")

            except Exception as e:
                print(f"[{index + 1}/{total_images}] ERROR processing {full_path}: {e}")
                continue

        # 5. Save and Summarize
        progress_window.destroy()
        if not all_results:
            messagebox.showinfo("Finished", "Processing complete, but no valid images were processed.")
            print("--- [Check All] Finished: No valid images processed. ---")
            return

        base, ext = os.path.splitext(label_file_path)
        output_path = f"{base}_check.csv"
        pd.DataFrame(all_results).to_csv(output_path, index=False)
        
        total = len(all_results)
        summary = (f"Processing Complete!\n\nResults saved to: {output_path}\n\n"
                   f"--- Accuracy Summary ---\n"
                   f"Completely Correct (Green): {stats['green']} ({stats['green']/total:.2%})\n"
                   f"Partially Correct (Yellow - Transverse, Lobe OK): {stats['yellow']} ({stats['yellow']/total:.2%})\n"
                   f"Lobe Ambiguity (Blue - Longitudinal, Lobe Mismatch): {stats['blue']} ({stats['blue']/total:.2%})\n"
                   f"Incorrect (Red): {stats['red']} ({stats['red']/total:.2%})\n")
        messagebox.showinfo("Finished", summary)
        print(f"--- [Check All] Process Finished. Results saved to {output_path} ---")

    def _create_progress_bar(self, max_value):
        win = tk.Toplevel(self.master)
        win.title("Processing...")
        tk.Label(win, text="Checking all images, please wait...").pack(padx=20, pady=10)
        bar = ttk.Progressbar(win, orient='horizontal', length=300, mode='determinate', maximum=max_value)
        bar.pack(padx=20, pady=(0, 20))
        return win, bar

    def _parse_model_outputs(self, outputs):
        """Helper to parse model outputs into the top-2 prediction structure."""
        predictions = {}
        output_map = {
            'orientation_label': (outputs[4], self.orientation_labels),
            'lobe_label': (outputs[0], self.lobe_labels),
            'longitudinal_label': (outputs[1], self.longitudinal_labels),
            'transverse_label': (outputs[2], self.transverse_labels),
            'isthmus_label': (outputs[3], self.isthmus_labels),
        }
        for key, (logits, labels) in output_map.items():
            probs = torch.softmax(logits, dim=1).squeeze()
            k = min(2, len(labels))
            if k == 0: continue
            topk_probs, topk_indices = torch.topk(probs, k)
            predictions[key] = [(labels[idx.item()], prob.item()) for prob, idx in zip(topk_probs, topk_indices)]
        return predictions

    def _run_yolo_and_draw_box(self, img):
        """Runs YOLO model and draws bounding boxes on the image."""
        try:
            from ultralytics import YOLO
            from PIL import ImageDraw

            yolo_path = self.yolo_model_path_var.get()

            # Load YOLO model only if needed
            if self.yolo_model is None:
                if not yolo_path or not os.path.exists(yolo_path):
                    messagebox.showerror("Error", f"YOLO model not found or path not set in config.yaml:\n{yolo_path}")
                    self.detect_trachea_var.set(False) # Uncheck to avoid repeated errors
                    return img
                self.yolo_model = YOLO(yolo_path)
                print(f"INFO: YOLO model loaded from {yolo_path}")

            # Run inference
            results = self.yolo_model(img, verbose=False)

            # Draw bounding box on a copy of the image
            img_with_box = img.copy().convert("RGB") # Ensure it's RGB for drawing
            draw = ImageDraw.Draw(img_with_box)

            # Process results
            for r in results:
                for box in r.boxes:
                    x1, y1, x2, y2 = box.xyxy[0]
                    conf = box.conf[0]
                    cls = box.cls[0]
                    label = f'{self.yolo_model.names[int(cls)]} {conf:.2f}'
                    
                    # Draw rectangle and label
                    draw.rectangle([x1, y1, x2, y2], outline="lime", width=2)
                    # Simple check to prevent label from going off-screen
                    text_y = y1 - 10 if y1 > 10 else y1
                    draw.text((x1, text_y), label, fill="lime")

            return img_with_box

        except ImportError:
            messagebox.showerror("Dependency Error", "The 'ultralytics' package is required for this feature.\nPlease install it via 'pip install ultralytics'.")
            self.detect_trachea_var.set(False)
            return img
        except Exception as e:
            messagebox.showerror("YOLO Detection Error", f"An error occurred during trachea detection:\n{e}")
            self.detect_trachea_var.set(False)
            return img

    def _on_detect_trachea_toggle(self):
        """Called when the detect trachea checkbox is toggled."""
        # Re-render the image, which will apply/remove the detection overlay
        self._show_image()

    def _on_run_regressor_toggle(self):
        """Called when the 3D regressor checkbox is toggled."""
        if self.run_regressor_var.get():
            # If checked, run regressor on the current image.
            self._run_regressor()
        else:
            # If unchecked, clear the regressor predictions and update UI.
            self.regressor_predictions = {}
            self.final_regressor_predictions = {}
            self.pairing_successful = False
            self._update_labels()

    def _run_regressor(self):
        """
        Orchestrator for the regression process. Finds pairs, runs inference,
        combines results, and updates the UI.
        """
        # --- 1. Initial Checks ---
        self.pairing_successful = False # Reset the flag for this run
        if not (0 <= self.current_index < len(self.image_paths)):
            messagebox.showinfo("Info", "Please load an image first.")
            self.run_regressor_var.set(False)
            return

        required_cols = ['access_no', 'nindex', 'orientation_label']
        if not all(col in self.dataframe.columns for col in required_cols):
            messagebox.showerror("Error", f"Label file must contain {required_cols} for paired analysis.")
            self.run_regressor_var.set(False)
            return
            
        # --- 2. Get Current Image Info ---
        current_row = self.dataframe.iloc[self.current_index]
        current_path = self.image_paths[self.current_index]

        # --- 3. Run Inference on Current Image ---
        try:
            current_coords = self._run_inference_on_image(current_path)
            if current_coords is None: # Inference failed
                self.run_regressor_var.set(False)
                return
            self.regressor_predictions = current_coords
        except Exception as e:
            messagebox.showerror("Regressor Error", f"Failed to run inference on current image:\n{e}")
            self.run_regressor_var.set(False)
            return

        # --- 4. Find and Process Paired Image ---
        access_no = current_row['access_no']
        nindex = current_row['nindex']
        
        # Find rows that match access_no and nindex but are not the current image
        pair_df = self.dataframe[
            (self.dataframe['access_no'] == access_no) &
            (self.dataframe['nindex'] == nindex) &
            (self.dataframe.index != self.current_index)
        ]
        
        # --- 5. Combine Results and Update Final Predictions ---
        if not pair_df.empty:
            paired_row = pair_df.iloc[0]
            paired_path = os.path.join(self.img_root.get(), paired_row['image_path'])
            print(f"INFO: Found paired image: {paired_path}")
            
            try:
                paired_coords = self._run_inference_on_image(paired_path)
                if paired_coords:
                    self.final_regressor_predictions = self._combine_paired_predictions(
                        current_coords, current_row['orientation_label'],
                        paired_coords, paired_row['orientation_label']
                    )
                else: # Paired inference failed, fall back to single
                    self.final_regressor_predictions = self._get_single_image_final_coords(current_coords, current_row['orientation_label'])
            except Exception as e:
                print(f"WARN: Failed to process paired image, falling back to single. Error: {e}")
                self.final_regressor_predictions = self._get_single_image_final_coords(current_coords, current_row['orientation_label'])
        else:
            print("INFO: No paired image found. Using single image result.")
            self.final_regressor_predictions = self._get_single_image_final_coords(current_coords, current_row['orientation_label'])

        # --- 6. Update UI ---
        self._update_labels()

    def _run_inference_on_image(self, image_path):
        """Runs the regressor model on a single image and returns coordinates."""
        model_path = self.model_regressor.get()
        if not os.path.exists(model_path):
            messagebox.showerror("Error", f"Regressor model file not found:\n{model_path}")
            return None
        
        if not os.path.exists(image_path):
            print(f"WARN: Image not found at path for inference: {image_path}")
            return None

        try:
            if self.regressor_model is None:
                self.regressor_model = ThyroidPositionRegressor()
                state_dict = torch.load(model_path, map_location=torch.device('cpu'))
                self.regressor_model.load_state_dict(state_dict)
                self.regressor_model.eval()
                print(f"INFO: Regressor model loaded from {model_path}")

            transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ])

            image = Image.open(image_path).convert('RGB')
            input_tensor = transform(image).unsqueeze(0)

            with torch.no_grad():
                outputs = self.regressor_model(input_tensor)

            coords = outputs.squeeze().cpu().numpy()
            return {'rl': coords[0], 'ud': coords[1], 'fb': coords[2]}

        except Exception as e:
            messagebox.showerror("Regressor Inference Error", f"An error occurred during model inference on {os.path.basename(image_path)}:\n{e}")
            self.regressor_model = None # Reset on error
            return None
            
    def _combine_paired_predictions(self, coords1, orientation1, coords2, orientation2):
        """Combines predictions from two paired images (transverse and longitudinal)."""
        # orientation_label: 0 is Longitudinal('纵切'), 1 is Transverse('横切')
        if orientation1 == orientation2:
            print("WARN: Paired images have the same orientation. Using first image's data.")
            return self._get_single_image_final_coords(coords1, orientation1)

        # If we get here, it means we have a valid longitudinal and transverse pair.
        self.pairing_successful = True
        
        long_coords = coords1 if orientation1 == 0 else coords2
        trans_coords = coords1 if orientation1 == 1 else coords2
        
        # RL from transverse, UD from longitudinal, FB is averaged
        combined = {
            'rl': trans_coords['rl'],
            'ud': long_coords['ud'],
            'fb': np.mean([long_coords['fb'], trans_coords['fb']])
        }
        print(f"INFO: Combined coordinates: RL={combined['rl']:.3f}, UD={combined['ud']:.3f}, FB={combined['fb']:.3f}")
        return combined

    def _get_single_image_final_coords(self, coords, orientation):
        """Creates a final coordinate dict from a single image, nulling invalid fields."""
        final_coords = coords.copy()
        if orientation == 1: # Transverse
            final_coords['ud'] = np.nan
        elif orientation == 0: # Longitudinal
            final_coords['rl'] = np.nan
        return final_coords

    def _assemble_regressor_position(self, rl, ud, fb):
        """Assembles a position string from regressed RLUDFB coordinates based on detailed mapping rules."""
        
        # 1. Determine Lobe and RL-based side component
        lobe_part = ""
        rl_side_part = ""
        if pd.isna(rl):
            lobe_part = "N/A"
        elif rl < 1/9:
            lobe_part, rl_side_part = "右叶", "外侧"
        elif rl < 2/9:
            lobe_part = "右叶"
        elif rl < 3/9:
            lobe_part = "右叶"
            rl_side_part = "近峡部" if fb < 0.4 else "内侧"
        elif rl < 4/9:
            lobe_part = "峡部偏右"
        elif rl < 5/9:
            lobe_part = "峡部"
        elif rl < 6/9:
            lobe_part = "峡部偏左"
        elif rl < 7/9:
            lobe_part = "左叶"
            rl_side_part = "近峡部" if fb < 0.4 else "内侧"
        elif rl < 8/9:
            lobe_part = "左叶"
        else:  # >= 8/9
            lobe_part, rl_side_part = "左叶", "外侧"

        # 2. Determine UD (vertical) component
        ud_part = ""
        if pd.isna(ud):
            pass # No UD component
        elif ud < 1/7:
            ud_part = "上极"
        elif ud < 2/7:
            ud_part = "上部"
        elif ud < 3/7:
            ud_part = "中上部"
        elif ud < 4/7:
            ud_part = "中部"
        elif ud < 5/7:
            ud_part = "中下部"
        elif ud < 6/7:
            ud_part = "下部"
        else:  # >= 6/7
            ud_part = "下极"

        # 3. Determine FB (anterior/posterior) component
        fb_part = ""
        if pd.isna(fb):
            pass
        elif fb < 0.125:
            fb_part = "前侧"
        elif fb < 0.375:
            fb_part = "近前侧"
        elif fb < 0.625:
            fb_part = ""  # Substantia central area - empty string
        elif fb < 0.875:
            fb_part = "近背侧"
        else:  # >= 0.875
            fb_part = "背侧"

        # 4. Cleanup Rule: If multiple "side" descriptions appear, keep the last one.
        # The FB component's side description takes precedence over the RL component's.
        final_side_part = fb_part if fb_part else rl_side_part

        # 5. Assemble the final position string in Lobe-UD-Side order
        final_parts = [lobe_part, ud_part, final_side_part]
        final_position = "".join(p for p in final_parts if p)

        return final_position if final_position else "N/A"


if __name__ == "__main__":
    root = tk.Tk()
    app = LabelViewer(root)
    root.mainloop() 