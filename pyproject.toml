[project]
name = "thyroid_ai_platform"
version = "1.0.0"
description = "甲状腺AI一体化平台"
authors = [
    {name = "Project Team", email = "<EMAIL>"}
]
dependencies = [
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    "pytorch-lightning>=2.0.0",
    "mlflow>=2.8.0",
    "wandb>=0.15.0",
    "dvc>=3.30.0",
    "pandas>=2.0.0",
    "streamlit>=1.30.0",
    "plotly>=5.18.0",
    "hydra-core>=1.3.0",
    "opencv-python>=4.8.0",
    "pillow>=10.0.0",
    "numpy>=1.24.0",
    "scikit-learn>=1.3.0",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "tqdm>=4.65.0",
    "pyyaml>=6.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=thyroid_ai_platform"

[tool.coverage.run]
source = ["thyroid_ai_platform"]
omit = ["tests/*", "**/__init__.py"]

[tool.black]
line-length = 100
target-version = ["py38"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3 