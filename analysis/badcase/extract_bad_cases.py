import pandas as pd
import sys

def extract_incorrect_cases(analyzed_file, output_file):
    """
    从分析结果文件中筛选出被评定为"不正确"的完整病例。

    Args:
        analyzed_file (str): 带有分析结果的Excel文件路径。
        output_file (str): 保存"不正确"病例的Excel文件路径。
    """
    print(f"正在从 '{analyzed_file}' 中加载分析结果...")
    try:
        df = pd.read_excel(analyzed_file)
    except FileNotFoundError:
        print(f"错误: 分析文件未找到 - '{analyzed_file}'", file=sys.stderr)
        print("请先运行 'analyze_model_results.py' 生成分析文件。", file=sys.stderr)
        return

    # 筛选出被评定为"不正确"的记录
    incorrect_df = df[df['accuracy_category'] == '不正确']
    
    if incorrect_df.empty:
        print("分析文件中没有找到标记为'不正确'的案例。")
        return

    # 获取这些"不正确"案例的唯一检查号 (access_no)
    incorrect_access_nos = incorrect_df['access_no'].unique()
    
    print(f"找到了 {len(incorrect_access_nos)} 个包含'不正确'预测的独立检查号 (access_no)。")
    print("正在提取这些检查号对应的所有记录...")

    # 从原始数据中筛选出与这些检查号相关的所有记录
    bad_cases_df = df[df['access_no'].isin(incorrect_access_nos)].copy()

    # 为了方便查看，将数据按照 access_no 和 sop_uid 排序
    bad_cases_df.sort_values(by=['access_no', 'sop_uid'], inplace=True)

    # 保存到新的Excel文件
    try:
        bad_cases_df.to_excel(output_file, index=False)
        print(f"已成功将 {len(bad_cases_df)} 条记录 ({len(incorrect_access_nos)} 个病例) 保存到 '{output_file}'")
    except Exception as e:
        print(f"保存文件时出错: {e}", file=sys.stderr)

if __name__ == "__main__":
    ANALYZED_FILE = 'sop_with_nodule_results_with_slice_model_v4_analyzed.xlsx'
    OUTPUT_FILE = 'sop_with_nodule_results_with_slice_model_v4_badcase.xlsx'
    extract_incorrect_cases(ANALYZED_FILE, OUTPUT_FILE) 