import pandas as pd
import numpy as np
import sys

# --- 1. 金标准(Ground Truth)解析逻辑 ---
# 基于 thyroid_label_validator.py 中的关键字提取逻辑

def parse_position(position_str):
    """
    从 position 描述字符串中解析出各个维度的标签。
    """
    if not isinstance(position_str, str) or position_str == "未知":
        return {"lobe": "未知", "longitudinal": "未知", "transverse": "未知", "isthmus": "未知"}

    # 叶位 (lobe)
    lobe = "未知"
    if "左叶" in position_str:
        lobe = "左叶"
    elif "右叶" in position_str:
        lobe = "右叶"
    elif "峡部" in position_str: # 包括"峡部"和"近峡部"
        lobe = "峡部"

    # 纵向 (longitudinal)
    longitudinal = "未知"
    if "上极" in position_str:
        longitudinal = "上极"
    elif "上部" in position_str:
        longitudinal = "上部"
    elif "中上部" in position_str:
        longitudinal = "中上部"
    elif "中部" in position_str:
        longitudinal = "中部"
    elif "中下部" in position_str:
        longitudinal = "中下部"
    elif "下部" in position_str:
        longitudinal = "下部"
    elif "下极" in position_str:
        longitudinal = "下极"
    if longitudinal == "未知":
        longitudinal = "无明确纵向位置"

    # 侧向 (transverse)
    transverse = "未知"
    if "前侧" in position_str:
        transverse = "前侧"
    elif "背侧" in position_str:
        transverse = "背侧"
    elif "外侧" in position_str:
        transverse = "外侧"
    elif "内侧" in position_str or "近峡部" in position_str: # "近峡部" 暗示 "内侧"
        transverse = "内侧"
    if transverse == "未知":
        transverse = "无明确侧向位置"

    # 峡部关系 (isthmus)
    isthmus = "非近峡部"
    if "近峡部" in position_str:
        isthmus = "近峡部"

    return {"lobe": lobe, "longitudinal": longitudinal, "transverse": transverse, "isthmus": isthmus}


# --- 2. 模糊匹配和评分标准 ---

# 定义同义词/近邻关系组。我们认为在同一个组内的标签是匹配的。
# 注意：这里的 key 是一个代表性的标签，value 是所有被认为与 key 等价的标签集合。
SYNONYM_GROUPS = {
    'lobe': {
        '右叶': {'右叶'},
        '左叶': {'左叶'},
        '峡部': {'峡部', '近峡部'} # "近峡部"在叶位上属于"峡部"
    },
    'transverse': {
        '内侧': {'内侧', '近峡部'}, # "近峡部"在侧向上等同于"内侧"
        '外侧': {'外侧'},
        '前侧': {'前侧'},
        '背侧': {'背侧'},
        '无明确侧向位置': {'无明确侧向位置'}
    }
}

def get_synonym_group_key(label_type, value):
    """查找一个值所属的同义词组代表键"""
    if label_type not in SYNONYM_GROUPS:
        return value # 如果没有定义同义词组，则直接返回值本身作为键
    
    for key, group in SYNONYM_GROUPS[label_type].items():
        if value in group:
            return key
    return None # 如果找不到，返回None


def calculate_accuracy_score(row):
    """
    为单行数据计算准确度得分和分类。
    只评估模型预测为"横切"的图像。
    """
    # 如果不是横切，则不评估
    if row['orientation_pred'] != '横切':
        return pd.Series([None, '非横切面不评估', 0.0])

    gt = row['gt_labels']
    
    # 如果金标准无法解析，则无法判断
    if gt['lobe'] == '未知':
        return pd.Series(['无法判断', '金标准无法解析', 0.0])

    score = 0
    
    # 1. 叶位评估 (最高3分)
    gt_lobe_key = get_synonym_group_key('lobe', gt['lobe'])
    pred_lobe1_key = get_synonym_group_key('lobe', row['lobe_pred_1'])
    pred_lobe2_key = get_synonym_group_key('lobe', row['lobe_pred_2'])
    
    if gt_lobe_key == pred_lobe1_key:
        score += 3  # Top-1 命中
    elif gt_lobe_key == pred_lobe2_key:
        score += 2  # Top-2 命中
    
    # 2. 侧向位置评估 (最高1分)
    gt_transverse_key = get_synonym_group_key('transverse', gt['transverse'])
    pred_transverse_key = get_synonym_group_key('transverse', row['transverse_pred'])
    if gt_transverse_key == pred_transverse_key:
        score += 1

    # 3. 峡部关系评估 (最高1分)
    if gt['isthmus'] == row['isthmus_pred']:
        score += 1

    # 根据总分进行分类
    if score == 5:
        category = "完全正确"
    elif score >= 3: # 3-4分
        category = "部分正确"
    elif score == 2: # 2分，通常意味着只有叶位Top-2正确
        category = "基本正确"
    else: # 0-1分
        category = "不正确"

    return pd.Series([category, f"总分: {score}", score])

# --- 3. 主分析流程 ---
def analyze_results(input_file):
    """
    主分析函数：加载数据，处理，并打印报告。
    """
    print(f"开始分析文件: {input_file}")
    
    try:
        df = pd.read_excel(input_file)
    except FileNotFoundError:
        print(f"错误: 文件未找到 - {input_file}", file=sys.stderr)
        return
    
    # 步骤1: 解析金标准 'position' 字段
    print("正在解析 'position' 列以提取金标准...")
    df['gt_labels'] = df['position'].apply(parse_position)

    # 步骤2: 计算每行的准确度得分和分类
    print("正在为每个横切面样本计算准确度...")
    df[['accuracy_category', 'accuracy_reason', 'accuracy_score']] = df.apply(
        calculate_accuracy_score, axis=1
    )

    # 步骤3: 生成并打印结果报告
    print("\n--- 模型位置判断准确率分析报告 ---")
    
    total_samples = len(df)
    print(f"总样本数: {total_samples}")
    
    transverse_samples_df = df[df['orientation_pred'] == '横切'].copy()
    num_transverse = len(transverse_samples_df)
    print(f"模型预测为'横切'的样本数: {num_transverse} ({num_transverse/total_samples:.2%})")

    unjudgeable_samples = transverse_samples_df[transverse_samples_df['accuracy_category'] == '无法判断']
    num_unjudgeable = len(unjudgeable_samples)
    print(f"  - 其中金标准无法解析的样本数: {num_unjudgeable}")
    
    # 从可评估的横切面样本中计算准确率
    evaluatable_df = transverse_samples_df.dropna(subset=['accuracy_category'])
    evaluatable_df = evaluatable_df[evaluatable_df['accuracy_category'] != '无法判断']
    num_evaluatable = len(evaluatable_df)
    
    if num_evaluatable == 0:
        print("没有可供评估的横切面样本。")
        return
        
    print(f"\n在 {num_evaluatable} 个可评估的横切面样本中:")
    
    accuracy_counts = evaluatable_df['accuracy_category'].value_counts()
    
    categories = ["完全正确", "部分正确", "基本正确", "不正确"]
    for category in categories:
        count = accuracy_counts.get(category, 0)
        percentage = count / num_evaluatable if num_evaluatable > 0 else 0
        print(f"  - {category}: {count} ({percentage:.2%})")

    # 计算一个综合准确率 (完全正确 + 部分正确)
    correct_ish_count = accuracy_counts.get("完全正确", 0) + accuracy_counts.get("部分正确", 0)
    correct_ish_accuracy = correct_ish_count / num_evaluatable if num_evaluatable > 0 else 0
    print(f"\n综合准确率 (完全正确 + 部分正确): {correct_ish_accuracy:.2%}")

    # 步骤4: 保存带有分析结果的新文件
    output_filename = input_file.replace('.xlsx', '_analyzed.xlsx')
    print(f"\n正在保存详细分析结果到: {output_filename}")
    # 为了更好的可读性，将字典解构成单独的列
    gt_df = df['gt_labels'].apply(pd.Series)
    gt_df.columns = ['gt_' + col for col in gt_df.columns]
    
    final_df = pd.concat([df.drop('gt_labels', axis=1), gt_df], axis=1)
    
    # 重新排列一下列的顺序，让分析结果更靠前
    cols = list(final_df.columns)
    analysis_cols = ['accuracy_category', 'accuracy_reason', 'accuracy_score']
    for col in reversed(analysis_cols):
        cols.insert(df.columns.get_loc('position')+1, cols.pop(cols.index(col)))
    final_df = final_df[cols]

    final_df.to_excel(output_filename, index=False)
    print("分析完成。")

if __name__ == "__main__":
    INPUT_FILE = "sop_with_nodule_results_with_slice_model_v4.xlsx"
    analyze_results(INPUT_FILE) 