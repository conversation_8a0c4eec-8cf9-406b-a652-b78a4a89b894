from dataclasses import dataclass
from typing import Dict, List, Optional, Tu<PERSON>

@dataclass
class BaseConfig:
    project_name: str = "thyroid_ai_platform"
    version: str = "1.0.0"
    
@dataclass 
class DataConfig:
    data_root: str
    image_size: Tuple[int, int] = (224, 224)
    batch_size: int = 32
    num_workers: int = 4
    train_val_split: float = 0.8
    
@dataclass
class ModelConfig:
    architecture: str = "resnet50"
    num_classes: Dict[str, int]
    pretrained: bool = True
    learning_rate: float = 1e-4
    weight_decay: float = 1e-4
    
@dataclass
class TrainingConfig:
    max_epochs: int = 100
    early_stopping_patience: int = 10
    gradient_clip_val: float = 1.0
    accumulate_grad_batches: int = 1
    precision: str = "16-mixed"
    
@dataclass
class ExperimentConfig:
    experiment_name: str
    tags: List[str]
    notes: Optional[str] = None
    log_every_n_steps: int = 50 